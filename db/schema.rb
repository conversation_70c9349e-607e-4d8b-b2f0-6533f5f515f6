# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_05_27_232114) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"
  enable_extension "pg_trgm"
  enable_extension "unaccent"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "approval_actions", force: :cascade do |t|
    t.bigint "approval_request_id", null: false
    t.bigint "approval_step_id", null: false
    t.string "user_id", null: false
    t.string "action", null: false
    t.text "comment"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["approval_request_id"], name: "index_approval_actions_on_approval_request_id"
    t.index ["approval_step_id", "user_id", "action"], name: "index_approval_actions_on_step_user_action"
    t.index ["approval_step_id"], name: "index_approval_actions_on_approval_step_id"
  end

  create_table "approval_requests", force: :cascade do |t|
    t.string "workflow_id", null: false
    t.string "workflow_name", null: false
    t.bigint "requestor_id", null: false
    t.string "approvable_type", null: false
    t.bigint "approvable_id", null: false
    t.integer "status", default: 0, null: false
    t.json "steps_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["approvable_type", "approvable_id"], name: "index_approval_requests_on_approvable"
    t.index ["requestor_id"], name: "index_approval_requests_on_requestor_id"
  end

  create_table "approval_steps", force: :cascade do |t|
    t.bigint "approval_request_id", null: false
    t.string "step_id", null: false
    t.string "name", null: false
    t.integer "sequence", null: false
    t.string "approval_type", default: "any", null: false
    t.json "approver_ids", default: [], null: false
    t.string "condition"
    t.boolean "skip_if_no_approvers", default: false
    t.string "description"
    t.string "dynamic_approver_method"
    t.text "selection_explanation"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["approval_request_id", "sequence"], name: "index_approval_steps_on_approval_request_id_and_sequence", unique: true
    t.index ["approval_request_id"], name: "index_approval_steps_on_approval_request_id"
  end

  create_table "attendance_devices", force: :cascade do |t|
    t.string "name", null: false
    t.string "adapter_type", null: false
    t.string "ip_address"
    t.integer "port"
    t.integer "status", default: 0, null: false
    t.string "location"
    t.jsonb "connection_config", default: {}
    t.jsonb "sync_config", default: {}
    t.jsonb "capabilities", default: {}
    t.datetime "last_seen_at"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["adapter_type", "status"], name: "index_attendance_devices_on_adapter_type_and_status"
    t.index ["adapter_type"], name: "index_attendance_devices_on_adapter_type"
    t.index ["capabilities"], name: "index_attendance_devices_on_capabilities", using: :gin
    t.index ["connection_config"], name: "index_attendance_devices_on_connection_config", using: :gin
    t.index ["ip_address"], name: "index_attendance_devices_on_ip_address"
    t.index ["name"], name: "index_attendance_devices_on_name", unique: true
    t.index ["status"], name: "index_attendance_devices_on_status"
  end

  create_table "attendance_events", force: :cascade do |t|
    t.bigint "employee_id", null: false
    t.integer "event_type", default: 2
    t.integer "activity_type", default: 0
    t.string "location"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "timestamp", default: 0, null: false
    t.boolean "potential_duplicate", default: false
    t.integer "source_device_id"
    t.text "raw_data"
    t.index ["employee_id", "timestamp"], name: "index_attendance_events_on_employee_id_and_timestamp"
    t.index ["employee_id"], name: "index_attendance_events_on_employee_id"
    t.index ["potential_duplicate"], name: "index_attendance_events_on_potential_duplicate"
    t.index ["source_device_id"], name: "index_attendance_events_on_source_device_id"
  end

  create_table "attendance_periods", force: :cascade do |t|
    t.bigint "employee_id", null: false
    t.date "date", null: false
    t.string "period_type", null: false
    t.integer "start_timestamp", null: false
    t.integer "end_timestamp", null: false
    t.integer "duration_minutes", null: false
    t.string "activity_type"
    t.boolean "is_predicted", default: false
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id", "date"], name: "index_attendance_periods_on_employee_id_and_date"
    t.index ["employee_id"], name: "index_attendance_periods_on_employee_id"
    t.index ["period_type"], name: "index_attendance_periods_on_period_type"
  end

  create_table "attendance_summaries", force: :cascade do |t|
    t.bigint "employee_id", null: false
    t.date "date", null: false
    t.datetime "first_check_in"
    t.datetime "last_check_out"
    t.integer "total_duration_minutes", default: 0
    t.string "work_status"
    t.integer "event_count", default: 0
    t.integer "undetermined_count", default: 0
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id", "date"], name: "index_attendance_summaries_on_employee_id_and_date", unique: true
    t.index ["employee_id"], name: "index_attendance_summaries_on_employee_id"
  end

  create_table "attendance_sync_logs", force: :cascade do |t|
    t.bigint "attendance_device_id", null: false
    t.bigint "triggered_by_id"
    t.bigint "employee_id"
    t.integer "status", default: 0, null: false
    t.integer "sync_type", default: 0, null: false
    t.string "operation_type"
    t.datetime "started_at"
    t.datetime "completed_at"
    t.text "sync_params"
    t.text "result_summary"
    t.text "error_details"
    t.integer "retry_count", default: 0
    t.datetime "next_retry_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["attendance_device_id", "employee_id", "operation_type"], name: "idx_sync_logs_device_employee_operation"
    t.index ["attendance_device_id", "started_at"], name: "idx_on_attendance_device_id_started_at_3faf70fc70"
    t.index ["attendance_device_id", "status"], name: "index_attendance_sync_logs_on_attendance_device_id_and_status"
    t.index ["attendance_device_id"], name: "index_attendance_sync_logs_on_attendance_device_id"
    t.index ["employee_id", "sync_type"], name: "index_attendance_sync_logs_on_employee_id_and_sync_type"
    t.index ["employee_id"], name: "index_attendance_sync_logs_on_employee_id"
    t.index ["operation_type"], name: "index_attendance_sync_logs_on_operation_type"
    t.index ["started_at"], name: "index_attendance_sync_logs_on_started_at"
    t.index ["status", "next_retry_at"], name: "index_attendance_sync_logs_on_status_and_next_retry_at"
    t.index ["status"], name: "index_attendance_sync_logs_on_status"
    t.index ["sync_type"], name: "index_attendance_sync_logs_on_sync_type"
    t.index ["triggered_by_id"], name: "index_attendance_sync_logs_on_triggered_by_id"
  end

  create_table "employee_device_mappings", force: :cascade do |t|
    t.bigint "employee_id", null: false
    t.bigint "attendance_device_id", null: false
    t.string "device_user_id", null: false
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["attendance_device_id", "device_user_id"], name: "idx_device_user_unique", unique: true
    t.index ["attendance_device_id"], name: "index_employee_device_mappings_on_attendance_device_id"
    t.index ["device_user_id"], name: "index_employee_device_mappings_on_device_user_id"
    t.index ["employee_id", "attendance_device_id"], name: "idx_employee_device_unique", unique: true
    t.index ["employee_id"], name: "index_employee_device_mappings_on_employee_id"
  end

  create_table "employee_search_views_data", primary_key: "employee_id", id: :bigint, default: nil, force: :cascade do |t|
    t.string "department"
    t.date "start_date"
    t.integer "status"
    t.bigint "user_id"
    t.string "name"
    t.string "email"
    t.text "search_document"
    t.text "phone"
  end

  create_table "employees", force: :cascade do |t|
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "department"
    t.date "start_date"
    t.integer "status", default: 0
    t.string "phone"
    t.string "device_code"
    t.index "((department)::text) gin_trgm_ops", name: "employees_department_trgm_idx", using: :gin
    t.index ["device_code"], name: "index_employees_on_device_code"
  end

  create_table "leaves", force: :cascade do |t|
    t.bigint "employee_id", null: false
    t.integer "leave_type", null: false
    t.date "start_date"
    t.date "end_date"
    t.text "reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "leave_duration", default: 0
    t.integer "status", default: 0
    t.index ["employee_id"], name: "index_leaves_on_employee_id"
    t.index ["status"], name: "index_leaves_on_status"
  end

  create_table "salary_calculation_details", force: :cascade do |t|
    t.bigint "salary_calculation_id", null: false
    t.string "detail_type", null: false
    t.string "category", null: false
    t.decimal "amount", precision: 10, scale: 2, null: false
    t.text "description", null: false
    t.integer "reference_id"
    t.string "reference_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["detail_type", "category"], name: "index_salary_calculation_details_on_detail_type_and_category"
    t.index ["reference_type", "reference_id"], name: "index_salary_calculation_details_on_reference"
    t.index ["salary_calculation_id"], name: "index_salary_calculation_details_on_salary_calculation_id"
  end

  create_table "salary_calculations", force: :cascade do |t|
    t.bigint "employee_id", null: false
    t.bigint "salary_package_id", null: false
    t.string "period", null: false
    t.date "period_start_date", null: false
    t.date "period_end_date", null: false
    t.decimal "gross_salary", precision: 10, scale: 2, null: false
    t.jsonb "deductions", default: {}, null: false
    t.decimal "net_salary", precision: 10, scale: 2, null: false
    t.integer "status", default: 0, null: false
    t.datetime "calculation_date"
    t.datetime "payment_date"
    t.bigint "approved_by_id"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "total_hours", precision: 10, scale: 2, default: "0.0"
    t.index ["approved_by_id"], name: "index_salary_calculations_on_approved_by_id"
    t.index ["employee_id", "period_start_date", "period_end_date"], name: "index_salary_calculations_on_employee_and_dates", unique: true
    t.index ["employee_id"], name: "index_salary_calculations_on_employee_id"
    t.index ["period"], name: "index_salary_calculations_on_period"
    t.index ["salary_package_id"], name: "index_salary_calculations_on_salary_package_id"
    t.index ["status"], name: "index_salary_calculations_on_status"
  end

  create_table "salary_packages", force: :cascade do |t|
    t.bigint "employee_id", null: false
    t.decimal "base_salary", precision: 10, scale: 2, null: false
    t.decimal "housing_allowance", precision: 10, scale: 2, default: "0.0"
    t.decimal "transportation_allowance", precision: 10, scale: 2, default: "0.0"
    t.decimal "other_allowances", precision: 10, scale: 2, default: "0.0"
    t.date "effective_date", null: false
    t.date "end_date"
    t.text "adjustment_reason"
    t.integer "previous_package_id"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id", "effective_date"], name: "index_salary_packages_on_employee_id_and_effective_date"
    t.index ["employee_id"], name: "index_salary_packages_on_employee_id"
    t.index ["previous_package_id"], name: "index_salary_packages_on_previous_package_id"
  end

  create_table "settings", force: :cascade do |t|
    t.string "namespace", default: "system", null: false
    t.string "key", null: false
    t.text "value", null: false
    t.text "description"
    t.boolean "is_editable", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["namespace", "key"], name: "index_settings_on_namespace_and_key", unique: true
  end

  create_table "social_security_configs", force: :cascade do |t|
    t.decimal "employee_rate", precision: 5, scale: 2, null: false
    t.decimal "employer_rate", precision: 5, scale: 2, null: false
    t.decimal "max_salary", precision: 10, scale: 2
    t.date "effective_date", null: false
    t.date "end_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["effective_date", "end_date"], name: "index_social_security_configs_on_effective_date_and_end_date"
    t.index ["effective_date"], name: "index_social_security_configs_on_effective_date"
  end

  create_table "tax_configs", force: :cascade do |t|
    t.string "name", null: false
    t.jsonb "config_data", default: {}, null: false
    t.date "effective_date", null: false
    t.date "end_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["effective_date", "end_date"], name: "index_tax_configs_on_effective_date_and_end_date"
    t.index ["effective_date"], name: "index_tax_configs_on_effective_date"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "approval_actions", "approval_requests"
  add_foreign_key "approval_actions", "approval_steps"
  add_foreign_key "approval_requests", "employees", column: "requestor_id"
  add_foreign_key "approval_steps", "approval_requests"
  add_foreign_key "attendance_events", "attendance_devices", column: "source_device_id"
  add_foreign_key "attendance_events", "employees"
  add_foreign_key "attendance_periods", "employees"
  add_foreign_key "attendance_summaries", "employees"
  add_foreign_key "attendance_sync_logs", "attendance_devices"
  add_foreign_key "attendance_sync_logs", "employees"
  add_foreign_key "attendance_sync_logs", "employees", column: "triggered_by_id"
  add_foreign_key "employee_device_mappings", "attendance_devices"
  add_foreign_key "employee_device_mappings", "employees"
  add_foreign_key "leaves", "employees"
  add_foreign_key "salary_calculation_details", "salary_calculations"
  add_foreign_key "salary_calculations", "employees"
  add_foreign_key "salary_calculations", "employees", column: "approved_by_id"
  add_foreign_key "salary_calculations", "salary_packages"
  add_foreign_key "salary_packages", "employees"

  create_view "employee_search_views", sql_definition: <<-SQL
      SELECT employee_id,
      department,
      start_date,
      status,
      user_id,
      name,
      email,
      phone,
      search_document
     FROM employee_search_views_data esd;
  SQL
  create_view "employee_search_data", materialized: true, sql_definition: <<-SQL
      SELECT employee_id,
      department,
      start_date,
      status,
      user_id,
      name,
      email,
      phone,
      search_document,
      to_tsvector('english'::regconfig, COALESCE(search_document, ''::text)) AS search_vector
     FROM employee_search_views esv;
  SQL
  add_index "employee_search_data", ["employee_id"], name: "employee_search_data_employee_id_idx"
  add_index "employee_search_data", ["search_vector"], name: "employee_search_data_search_vector_idx", using: :gin

end
