class CreateAttendanceSyncLogs < ActiveRecord::Migration[8.0]
  def change
    create_table :attendance_sync_logs do |t|
      t.references :attendance_device, null: false, foreign_key: true
      t.references :triggered_by, null: true, foreign_key: { to_table: :employees }
      t.integer :status, null: false, default: 0
      t.integer :sync_type, null: false, default: 0
      t.datetime :started_at
      t.datetime :completed_at
      t.text :sync_params
      t.text :result_summary
      t.text :error_details

      t.timestamps
    end

    add_index :attendance_sync_logs, :status
    add_index :attendance_sync_logs, :sync_type
    add_index :attendance_sync_logs, :started_at
    add_index :attendance_sync_logs, [:attendance_device_id, :status]
    add_index :attendance_sync_logs, [:attendance_device_id, :started_at]
  end
end
