class CreateDeviceSyncQueue < ActiveRecord::Migration[8.0]
  def change
    create_table :device_sync_queues do |t|
      t.references :attendance_device, null: false, foreign_key: { to_table: :attendance_devices }
      t.references :employee, null: false, foreign_key: true
      t.string :operation_type, null: false # 'create_user', 'update_user', 'delete_user'
      t.string :status, default: 'pending' # 'pending', 'processing', 'completed', 'failed'
      t.jsonb :operation_data, null: false
      t.text :error_message
      t.integer :retry_count, default: 0
      t.timestamp :next_retry_at
      t.timestamp :completed_at
      t.timestamps
    end

    add_index :device_sync_queues, [:status, :next_retry_at]
    add_index :device_sync_queues, [:attendance_device_id, :status]
    add_index :device_sync_queues, :operation_type
  end
end
