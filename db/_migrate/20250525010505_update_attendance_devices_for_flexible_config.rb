class UpdateAttendanceDevicesForFlexibleConfig < ActiveRecord::Migration[8.0]
  def up
    # Add new adapter_type column
    add_column :attendance_devices, :adapter_type, :string

    # Migrate existing device_type enum values to adapter_type strings
    execute <<-SQL
      UPDATE attendance_devices
      SET adapter_type = CASE device_type
        WHEN 0 THEN 'zkteco'
        WHEN 3 THEN 'generic_http'
        WHEN 4 THEN 'file_import'
        ELSE 'unknown'
      END
    SQL

    # Make adapter_type not null
    change_column_null :attendance_devices, :adapter_type, false

    # Convert text columns to jsonb
    # First, convert existing JSON strings to proper JSONB
    execute <<-SQL
      ALTER TABLE attendance_devices
      ADD COLUMN connection_config_new JSONB DEFAULT '{}',
      ADD COLUMN sync_config_new JSONB DEFAULT '{}',
      ADD COLUMN capabilities_new JSONB DEFAULT '{}'
    SQL

    # Migrate existing data
    execute <<-SQL
      UPDATE attendance_devices SET
        connection_config_new = CASE
          WHEN connection_config IS NOT NULL AND connection_config != ''
          THEN connection_config::jsonb
          ELSE '{}'::jsonb
        END,
        sync_config_new = CASE
          WHEN sync_config IS NOT NULL AND sync_config != ''
          THEN sync_config::jsonb
          ELSE '{}'::jsonb
        END,
        capabilities_new = CASE
          WHEN capabilities IS NOT NULL AND capabilities != ''
          THEN capabilities::jsonb
          ELSE '{}'::jsonb
        END
    SQL

    # Drop old columns and rename new ones
    remove_column :attendance_devices, :connection_config
    remove_column :attendance_devices, :sync_config
    remove_column :attendance_devices, :capabilities
    remove_column :attendance_devices, :device_type

    rename_column :attendance_devices, :connection_config_new, :connection_config
    rename_column :attendance_devices, :sync_config_new, :sync_config
    rename_column :attendance_devices, :capabilities_new, :capabilities

    # Add indexes
    add_index :attendance_devices, :adapter_type
    add_index :attendance_devices, [:adapter_type, :status]
    add_index :attendance_devices, :connection_config, using: :gin
    add_index :attendance_devices, :capabilities, using: :gin
  end

  def down
    # Reverse migration
    remove_index :attendance_devices, :connection_config
    remove_index :attendance_devices, :capabilities
    remove_index :attendance_devices, [:adapter_type, :status]
    remove_index :attendance_devices, :adapter_type

    # Add back device_type as integer
    add_column :attendance_devices, :device_type, :integer, default: 0

    # Convert adapter_type back to device_type enum
    execute <<-SQL
      UPDATE attendance_devices
      SET device_type = CASE adapter_type
        WHEN 'zkteco' THEN 0
        WHEN 'generic_http' THEN 3
        WHEN 'file_import' THEN 4
        ELSE 0
      END
    SQL

    # Convert jsonb back to text
    add_column :attendance_devices, :connection_config_old, :text
    add_column :attendance_devices, :sync_config_old, :text
    add_column :attendance_devices, :capabilities_old, :text

    execute <<-SQL
      UPDATE attendance_devices SET
        connection_config_old = connection_config::text,
        sync_config_old = sync_config::text,
        capabilities_old = capabilities::text
    SQL

    remove_column :attendance_devices, :connection_config
    remove_column :attendance_devices, :sync_config
    remove_column :attendance_devices, :capabilities
    remove_column :attendance_devices, :adapter_type

    rename_column :attendance_devices, :connection_config_old, :connection_config
    rename_column :attendance_devices, :sync_config_old, :sync_config
    rename_column :attendance_devices, :capabilities_old, :capabilities
  end
end
