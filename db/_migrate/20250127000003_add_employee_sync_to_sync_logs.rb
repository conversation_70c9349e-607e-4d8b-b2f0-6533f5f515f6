class AddEmployeeSyncToSyncLogs < ActiveRecord::Migration[8.0]
  def change
    # Add employee reference for employee-specific sync operations
    add_reference :attendance_sync_logs, :employee, foreign_key: true, null: true

    # Add operation type for employee sync operations
    add_column :attendance_sync_logs, :operation_type, :string, null: true

    # Add retry tracking for failed operations
    add_column :attendance_sync_logs, :retry_count, :integer, default: 0
    add_column :attendance_sync_logs, :next_retry_at, :timestamp, null: true

    # Add indexes for performance
    add_index :attendance_sync_logs, [:employee_id, :sync_type]
    add_index :attendance_sync_logs, [:status, :next_retry_at]
    add_index :attendance_sync_logs, :operation_type
    add_index :attendance_sync_logs, [:attendance_device_id, :employee_id, :operation_type],
              name: 'idx_sync_logs_device_employee_operation'
  end
end
