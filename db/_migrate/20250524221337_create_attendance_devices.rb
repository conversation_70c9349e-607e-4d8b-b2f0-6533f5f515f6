class CreateAttendanceDevices < ActiveRecord::Migration[8.0]
  def change
    create_table :attendance_devices do |t|
      t.string :name, null: false
      t.string :adapter_type, null: false
      t.string :ip_address
      t.integer :port
      t.integer :status, null: false, default: 0
      t.integer :location_id, null: true
      t.jsonb :connection_config, default: {}
      t.jsonb :sync_config, default: {}
      t.jsonb :capabilities, default: {}
      t.datetime :last_seen_at

      t.timestamps
    end

    add_index :attendance_devices, :name, unique: true
    add_index :attendance_devices, :adapter_type
    add_index :attendance_devices, :status
    add_index :attendance_devices, :ip_address
    add_index :attendance_devices, [:adapter_type, :status]
    add_index :attendance_devices, :connection_config, using: :gin
    add_index :attendance_devices, :capabilities, using: :gin
  end
end
