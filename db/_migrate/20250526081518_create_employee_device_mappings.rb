class CreateEmployeeDeviceMappings < ActiveRecord::Migration[8.0]
  def change
    create_table :employee_device_mappings do |t|
      t.references :employee, null: false, foreign_key: true
      t.references :attendance_device, null: false, foreign_key: { to_table: :attendance_devices }
      t.string :device_user_id, null: false
      t.text :notes, null: true
      t.timestamps
    end

    # Ensure one employee can only have one mapping per device
    add_index :employee_device_mappings, [:employee_id, :attendance_device_id], 
              unique: true, name: 'idx_employee_device_unique'
    
    # Ensure one device user ID can only belong to one employee per device
    add_index :employee_device_mappings, [:attendance_device_id, :device_user_id], 
              unique: true, name: 'idx_device_user_unique'
    
    # Performance index for lookups
    add_index :employee_device_mappings, :device_user_id
  end
end
