class CreateAttendanceCommandExecutions < ActiveRecord::Migration[8.0]
  def change
    create_table :attendance_command_executions do |t|
      # Foreign key to attendance devices
      t.references :device, null: false, foreign_key: { to_table: :attendance_devices }

      # Command details
      t.string :command_name, null: false
      t.jsonb :parameters, default: {}

      # Execution status
      t.integer :status, default: 0, null: false # 0: running, 1: completed, 2: failed

      # Result data
      t.jsonb :result, default: {}

      # Timing information
      t.datetime :started_at
      t.datetime :completed_at

      # Audit information
      t.references :executed_by, null: true, foreign_key: { to_table: :employees }

      t.timestamps
    end

    # Add indexes for performance
    add_index :attendance_command_executions, :device_id
    add_index :attendance_command_executions, :command_name
    add_index :attendance_command_executions, :status
    add_index :attendance_command_executions, :started_at
    add_index :attendance_command_executions, [:device_id, :started_at]
  end
end
