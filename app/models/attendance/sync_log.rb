module Attendance
  class SyncLog < ApplicationRecord
    include Athar::Commons::Models::Concerns::Ransackable

    # Use the existing table name for compatibility
    self.table_name = 'attendance_sync_logs'

    # Status enum
    enum :status, {
      pending: 0,
      running: 1,
      success: 2,
      failed: 3,
      partial: 4
    }

    # Sync type enum
    enum :sync_type, {
      manual: 0,
      scheduled: 1,
      real_time: 2,
      retry: 3,
      employee_sync: 4,
      bulk_employee_sync: 5
    }

    # Employee sync operation types
    EMPLOYEE_OPERATIONS = %w[create_user update_user delete_user].freeze

    # Retry configuration for employee sync
    MAX_RETRIES = 3
    RETRY_DELAYS = [5.minutes, 15.minutes, 1.hour].freeze

    # Associations
    belongs_to :device, class_name: 'Attendance::Device', foreign_key: :attendance_device_id
    belongs_to :employee, optional: true
    belongs_to :triggered_by, class_name: 'Employee', optional: true

    # Legacy association for backward compatibility
    belongs_to :attendance_device, class_name: 'Attendance::Device', foreign_key: :attendance_device_id

    # Validations
    validates :status, presence: true
    validates :sync_type, presence: true
    validates :started_at, presence: true

    # Scopes
    scope :recent, -> { order(created_at: :desc) }
    scope :successful, -> { where(status: :success) }
    scope :failed, -> { where(status: :failed) }
    scope :for_device, ->(device) { where(attendance_device_id: device.is_a?(Integer) ? device : device.id) }
    scope :for_date_range, ->(start_date, end_date) {
      where(started_at: start_date.beginning_of_day..end_date.end_of_day)
    }

    # JSON fields
    serialize :sync_params, coder: JSON
    serialize :result_summary, coder: JSON
    serialize :error_details, coder: JSON

    # Callbacks
    before_create :set_started_at
    after_initialize :ensure_json_fields

    # Instance methods
    def duration
      return nil unless started_at && completed_at
      completed_at - started_at
    end

    def duration_in_words
      return 'Not completed' unless duration

      if duration < 60
        "#{duration.round(2)} seconds"
      else
        "#{(duration / 60).round(2)} minutes"
      end
    end

    def success_rate
      return 0 unless result_summary['total_processed']&.positive?

      success_count = result_summary['success_count'] || 0
      total_count = result_summary['total_processed']

      (success_count.to_f / total_count * 100).round(2)
    end

    def mark_as_running!
      update!(status: :running, started_at: Time.current)
    end

    def mark_as_completed!(results)
      self.completed_at = Time.current
      self.result_summary = results

      # Determine final status based on results
      if results[:failure] == 0
        self.status = :success
      elsif results[:success] == 0
        self.status = :failed
      else
        self.status = :partial
      end

      save!
    end

    def mark_as_failed!(error)
      self.completed_at = Time.current
      self.status = :failed
      self.error_details = {
        'error_class' => error.class.name,
        'error_message' => error.message,
        'error_backtrace' => error.backtrace&.first(10)
      }
      save!
    end

    def add_error(error_message, context = {})
      self.error_details ||= { 'errors' => [] }
      self.error_details['errors'] << {
        'message' => error_message,
        'context' => context,
        'timestamp' => Time.current.iso8601
      }
      save!
    end

    def has_errors?
      error_details.present? && error_details['errors'].present?
    end

    def error_count
      return 0 unless has_errors?
      error_details['errors'].size
    end

    def primary_error
      return nil unless has_errors?
      error_details['errors'].first['message']
    end

    # Class methods
    def self.create_for_sync(device, sync_type, params = {}, triggered_by = nil)
      create!(
        device: device,
        sync_type: sync_type,
        sync_params: params,
        triggered_by: triggered_by,
        status: :pending,
        started_at: Time.current
      )
    end

    # Employee sync methods
    def self.create_for_employee_sync(device, employee, operation, triggered_by = nil)
      # Remove any existing pending sync for same employee/device/operation
      where(
        device: device,
        employee: employee,
        sync_type: [:employee_sync],
        operation_type: operation,
        status: [:pending, :failed]
      ).destroy_all

      # Create new sync request
      create!(
        device: device,
        employee: employee,
        sync_type: :employee_sync,
        operation_type: operation,
        sync_params: build_employee_operation_data(employee, device, operation),
        triggered_by: triggered_by,
        status: :pending,
        started_at: Time.current
      )
    end

    def self.build_employee_operation_data(employee, device, operation)
      case operation.to_s
      when 'create_user', 'update_user'
        {
          operation: operation,
          employee_id: employee.id,
          device_user_id: employee.device_code_for(device) || employee.id.to_s,
          user_data: {
            name: employee.user&.name || "Employee #{employee.id}",
            privilege: 0, # Regular user
            password: nil,
            group_id: "1",
            card_number: nil
          }
        }
      when 'delete_user'
        {
          operation: operation,
          employee_id: employee.id,
          device_user_id: employee.device_code_for(device) || employee.id.to_s
        }
      end
    end

    # Scopes for employee sync
    scope :employee_syncs, -> { where(sync_type: [:employee_sync, :bulk_employee_sync]) }
    scope :ready_for_retry, -> {
      where(status: :failed)
        .where('next_retry_at IS NULL OR next_retry_at <= ?', Time.current)
        .where('retry_count < ?', MAX_RETRIES)
    }

    def self.cleanup_old_logs(days_to_keep = 30)
      where('created_at < ?', days_to_keep.days.ago).delete_all
    end

    def self.success_rate_for_device(device, days = 7)
      logs = for_device(device).where('created_at > ?', days.days.ago)
      return 100 if logs.empty?

      successful_logs = logs.successful.count
      total_logs = logs.count

      (successful_logs.to_f / total_logs * 100).round(2)
    end

    def self.average_sync_time_for_device(device, days = 7)
      completed_logs = for_device(device)
                         .where('created_at > ? AND completed_at IS NOT NULL', days.days.ago)

      return 0 if completed_logs.empty?

      total_duration = completed_logs.sum { |log| log.duration || 0 }
      average_seconds = total_duration / completed_logs.count

      average_seconds.round(2)
    end

    # Employee sync instance methods (public)
    def is_employee_sync?
      employee_sync? || bulk_employee_sync?
    end

    def can_retry?
      is_employee_sync? && (retry_count || 0) < MAX_RETRIES
    end

    def mark_failed_with_retry!(error_message)
      self.retry_count = (retry_count || 0) + 1

      if can_retry?
        self.status = :failed
        self.error_details = { error: error_message, retry_count: retry_count }
        self.next_retry_at = Time.current + RETRY_DELAYS[retry_count - 1]
      else
        self.status = :failed
        self.error_details = { error: error_message, retry_count: retry_count, max_retries_reached: true }
        self.next_retry_at = nil
      end

      save!
    end

    def operation_description
      return sync_type.humanize unless is_employee_sync?

      employee_name = employee&.user&.name || "Employee #{employee&.id}"
      "#{operation_type&.humanize} #{employee_name} on #{device&.name}"
    end

    private

    def set_started_at
      self.started_at ||= Time.current
    end

    def ensure_json_fields
      self.sync_params ||= {}
      self.result_summary ||= {}
      self.error_details ||= {}
    end
  end
end
