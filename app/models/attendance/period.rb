module Attendance
  class Period < ApplicationRecord
    include Athar::Commons::Models::Concerns::Ransackable

    # Use the existing table name for compatibility
    self.table_name = 'attendance_periods'

    belongs_to :employee

    validates :date, presence: true
    validates :period_type, presence: true
    validates :start_timestamp, presence: true
    validates :end_timestamp, presence: true
    validates :duration_minutes, presence: true

    # Period types
    PERIOD_TYPES = {
      work: 'work',
      break: 'break',
      late: 'late',
      early_departure: 'early_departure',
      early_arrival: 'early_arrival'
    }

    # Scopes
    scope :for_date, ->(date) { where(date: date) }
    scope :for_date_range, ->(start_date, end_date) { where(date: start_date..end_date) }
    scope :by_type, ->(type) { where(period_type: type) }
    scope :work_periods, -> { where(period_type: PERIOD_TYPES[:work]) }
    scope :break_periods, -> { where(period_type: PERIOD_TYPES[:break]) }
    scope :late_periods, -> { where(period_type: PERIOD_TYPES[:late]) }
    scope :early_departure_periods, -> { where(period_type: PERIOD_TYPES[:early_departure]) }
    scope :early_arrival_periods, -> { where(period_type: PERIOD_TYPES[:early_arrival]) }
    scope :predicted, -> { where(is_predicted: true) }

    # Helper methods for working with timestamps

    # Convert the integer timestamps to Time objects
    def start_time
      Time.at(start_timestamp) if start_timestamp.present?
    end

    def end_time
      Time.at(end_timestamp) if end_timestamp.present?
    end

    # Format the timestamps for display
    def formatted_start_time(format = "%H:%M")
      start_time&.strftime(format)
    end

    def formatted_end_time(format = "%H:%M")
      end_time&.strftime(format)
    end

    # Format the duration
    def formatted_duration
      hours = duration_minutes / 60
      minutes = duration_minutes % 60

      if hours > 0
        "#{hours}h #{minutes}m"
      else
        "#{minutes}m"
      end
    end

    # Class methods for period management
    # Use the sophisticated PeriodService for calculations
    def self.calculate_for_employee_date(employee, date, incomplete_day = false)
      Attendance::PeriodService.new(employee, date, incomplete_day).calculate_periods
    end

    def self.recalculate_for_date_range(employee, start_date, end_date)
      (start_date..end_date).each do |date|
        incomplete_day = (date == Date.today)
        calculate_for_employee_date(employee, date, incomplete_day)
      end
    end
  end
end
