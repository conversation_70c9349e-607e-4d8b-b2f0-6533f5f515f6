# frozen_string_literal: true

module Attendance
  class CommandExecution < ApplicationRecord
    self.table_name = 'attendance_command_executions'

    # Associations
    belongs_to :device, class_name: 'Attendance::Devi<PERSON>'
    belongs_to :executed_by, class_name: 'Employee', optional: true

    # Enums
    enum status: {
      running: 0,
      completed: 1,
      failed: 2
    }

    # Validations
    validates :command_name, presence: true
    validates :status, presence: true

    # Scopes
    scope :recent, -> { order(created_at: :desc) }
    scope :for_device, ->(device) { where(device: device) }
    scope :by_command, ->(command) { where(command_name: command) }

    # Instance methods
    def duration
      return nil unless started_at && completed_at
      completed_at - started_at
    end

    def success?
      completed?
    end

    def failure?
      failed?
    end

    def in_progress?
      running?
    end

    # Mark execution as completed with result
    def mark_completed!(result)
      update!(
        status: :completed,
        result: result.as_json,
        completed_at: Time.current
      )
    end

    # Mark execution as failed with result
    def mark_failed!(result)
      update!(
        status: :failed,
        result: result.as_json,
        completed_at: Time.current
      )
    end

    # Get result as CommandResult object
    def command_result
      return nil unless result.present?

      Attendance::CommandResult.new(
        success: result['success'] || false,
        message: result['message'],
        error: result['error'],
        details: result['details'] || {}
      )
    end
  end
end