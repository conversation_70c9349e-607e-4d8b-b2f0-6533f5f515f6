module Attendance
  class CommandExecution < ApplicationRecord
    # Associations
    belongs_to :device, class_name: 'Attendance::Device'
    belongs_to :executed_by, class_name: 'Employee', optional: true

    # Validations
    validates :command_name, presence: true

    # Enums for status
    enum :status, { running: "running", completed: "completed", failed: "failed" }

    # Scopes
    scope :recent, -> { order(created_at: :desc).limit(20) }

    # Store result as CommandResult when retrieving from database
    def result
      return nil if self[:result].nil?

      Attendance::CommandResult.new(self[:result])
    end
  end
end

