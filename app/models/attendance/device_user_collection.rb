# frozen_string_literal: true

module Attendance
  class DeviceUserCollection
    include Athar::Commons::ActiveStruct::Collection
    
    collection_item_class DeviceUser
    
    # Class method to create from device hashes with Ransack support
    def self.from_device_hashes(hashes)
      collection = new
      hashes.each do |hash|
        collection << DeviceUser.from_device_hash(hash)
      end
      collection
    end
  end
end
