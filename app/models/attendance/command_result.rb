module Attendance
  # Defines a standardized structure for command execution results.
  # Inherits from a common ActiveStruct base if available, otherwise
  # this would be a plain Ruby object or use a similar light-weight structure library.
  class CommandResult < Athar::Commons::ActiveStruct::Base
    attribute :success, :boolean, default: false
    attribute :message, :string # General message, can be used for success or error summary
    attribute :error, :string   # Specific error message if success is false
    attribute :details, :hash, default: {} # Additional data or details

    # Helper class method to create a success result
    def self.success(message = nil, details = {})
      new(success: true, message: message, details: details)
    end

    # Helper class method to create a failure result
    def self.failure(error = nil, details = {})
      # Ensure message also gets populated for consistency if error is the primary field for failure
      message_val = error || "Command execution failed."
      new(success: false, message: message_val, error: error, details: details)
    end

    # Instance method to check for success
    def success?
      success
    end

    # Instance method to check for failure
    def failure?
      !success
    end
  end
end

