# frozen_string_literal: true

module Attendance
  class CommandResult < Athar::Commons::ActiveStruct::Base
    attribute :success, :boolean, default: false
    attribute :message, :string
    attribute :error, :string
    attribute :details, :hash, default: {}

    # Helper methods for creating standard responses
    def self.success(message = nil, details = {})
      new(success: true, message: message, details: details)
    end

    def self.failure(error = nil, details = {})
      new(success: false, error: error, details: details)
    end

    # Helper methods for checking result
    def failed?
      !success
    end

    def succeeded?
      success
    end

    # Convert to JSON for API responses
    def as_json(options = {})
      {
        success: success,
        message: message,
        error: error,
        details: details
      }.compact
    end
  end
end