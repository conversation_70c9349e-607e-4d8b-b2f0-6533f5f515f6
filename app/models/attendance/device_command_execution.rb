module Attendance
  class DeviceCommandExecution < ApplicationRecord
    belongs_to :device, class_name: 'Attendance::Device'
    belongs_to :executed_by, class_name: 'AuthUser', optional: true

    # Enum for status (optional, but good practice if you have a fixed set of statuses)
    # enum status: { pending: 'pending', in_progress: 'in_progress', success: 'success', failed: 'failed' }

    validates :command_name, presence: true
    validates :status, presence: true

    # Store parameters as JSON
    serialize :parameters, JSON

    scope :for_device, ->(device_id) { where(device_id: device_id) }
    scope :ordered_by_execution_date, -> { order(executed_at: :desc) }
  end
end

