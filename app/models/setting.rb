class Setting < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  validates :namespace, presence: true
  validates :key, presence: true, uniqueness: { scope: :namespace }
  validates :value, presence: true

  # Class method to get a setting with default
  def self.get(namespace, key, default = nil)
    begin
      setting = find_by(namespace: namespace, key: key)
      setting ? setting.value : default
    rescue => e
      Rails.logger.error("Error getting setting #{namespace}.#{key}: #{e.message}")
      default
    end
  end

  # Class method to set a setting
  def self.set(namespace, key, value, description = nil, is_editable = true)
    setting = find_or_initialize_by(namespace: namespace, key: key)
    setting.value = value
    setting.description = description if description
    setting.is_editable = is_editable
    setting.save
  end

  # Helper methods for specific attendance settings
  def self.attendance_work_start_time
    get("attendance", "work_start_time", "09:00")
  end

  def self.attendance_work_end_time
    get("attendance", "work_end_time", "17:00")
  end

  def self.attendance_duplicate_threshold_seconds
    value = get("attendance", "duplicate_threshold_seconds", "60")
    value.is_a?(Integer) ? value : value.to_i
  end

  def self.attendance_required_work_minutes
    value = get("attendance", "required_work_minutes", "480")
    value.is_a?(Integer) ? value : value.to_i
  end

  def self.attendance_break_threshold_minutes
    value = get("attendance", "break_threshold_minutes", "120")
    value.is_a?(Integer) ? value : value.to_i
  end

  # Convert work start/end times to timestamps for a specific date
  def self.attendance_work_start_timestamp(date)
    time_str = attendance_work_start_time
    hours, minutes = time_str.split(":").map(&:to_i)

    date.to_time.change(hour: hours, min: minutes).to_i
  end

  def self.attendance_work_end_timestamp(date)
    time_str = attendance_work_end_time
    hours, minutes = time_str.split(":").map(&:to_i)

    date.to_time.change(hour: hours, min: minutes).to_i
  end

  # Get all settings for a namespace
  def self.for_namespace(namespace)
    where(namespace: namespace)
  end
end
