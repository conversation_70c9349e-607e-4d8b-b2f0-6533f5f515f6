class EmployeeSearchData < ApplicationRecord
  include PgSearch::Model

  # This is a materialized view, not a regular table
  self.primary_key = 'employee_id'

  # Disable STI
  self.inheritance_column = :_type_disabled

  # Full-text search configuration
  pg_search_scope :full_text_search,
                  against: :search_document,
                  using: {
                    tsearch: {
                      dictionary: 'english',
                      tsvector_column: 'search_vector'
                    },
                    trigram: { threshold: 0.1 }
                  }

  # Class method to refresh the materialized view
  def self.refresh
    connection.execute("REFRESH MATERIALIZED VIEW employee_search_data")
  end

  # Helper method to safely convert a value for use in SQL
  # Handles nil, empty strings, integers, and strings appropriately
  # @param value [Object] The value to convert
  # @param allow_string [<PERSON><PERSON><PERSON>] Whether to allow string values (default: false)
  # @return [String] SQL-safe representation of the value
  def self.sql_safe_value(value, allow_string = false)
    if value.nil? || value.to_s.strip.empty?
      # Nil or empty string should be NULL in the database
      'NULL'
    elsif value.is_a?(Integer)
      # Integer values can be used directly
      value.to_s
    elsif value.is_a?(String) && value.to_i.to_s == value
      # If it's a string that can be converted to an integer, use the integer value
      # This handles cases like "0" that should be stored as 0
      value.to_i.to_s
    elsif value.is_a?(String) && value.to_s == 'active'
      # Handle 'active' string specifically (maps to 0 in the enum)
      '0'
    elsif value.is_a?(String) && value.to_s == 'inactive'
      # Handle 'inactive' string specifically (maps to 1 in the enum)
      '1'
    elsif value.is_a?(String) && value.to_s == 'pending'
      # Handle 'pending' string specifically (maps to 2 in the enum)
      '2'
    elsif value.is_a?(String) && allow_string
      # For strings, quote them if allowed
      connection.quote(value)
    elsif value.is_a?(String)
      # For strings when not allowed, try to convert to integer or use NULL
      begin
        Integer(value).to_s
      rescue ArgumentError
        'NULL'
      end
    else
      # Any other type should be NULL
      'NULL'
    end
  end

  # Class method to update the base view data
  def self.update_base_view_data
    # Create the table to back the view if it doesn't exist
    unless connection.table_exists?('employee_search_views_data')
      connection.execute(<<-SQL)
        CREATE TABLE employee_search_views_data (
          employee_id bigint PRIMARY KEY,
          department integer,
          start_date date,
          status integer,
          user_id bigint,
          name text,
          email text,
          phone text,
          search_document text
        )
      SQL
    end

    # Sync data from employees
    Employee.all.each do |employee|
      # Try to get user data
      name = nil
      email = nil

      if employee.respond_to?(:user_data) && employee.user_data.present?
        name = employee.name
        email = employee.email
      end

      # Create search document
      search_document = [
        employee.department.to_s,
        employee.department_name.to_s, # Add department name for better search
        name.to_s,
        email.to_s,
        employee.phone.to_s
      ].join(' ')

      # Handle status value safely using the helper method
      # The status field is an enum (integer) in the database
      status_value = sql_safe_value(employee.status)

      # Handle user_id value safely using the helper method
      # The user_id field is a bigint in the database
      user_id_value = sql_safe_value(employee.user_id)

      # Build the complete SQL statement
      sql = <<-SQL
        INSERT INTO employee_search_views_data (
          employee_id, department, start_date, status, user_id, name, email, phone, search_document
        ) VALUES (
          #{employee.id},
          #{Employee.departments[employee.department] || 'NULL'},
          #{connection.quote(employee.start_date)},
          #{status_value},
          #{user_id_value},
          #{connection.quote(name)},
          #{connection.quote(email)},
          #{connection.quote(employee.phone.to_s)},
          #{connection.quote(search_document)}
        )
        ON CONFLICT (employee_id) DO UPDATE SET
          department = EXCLUDED.department,
          start_date = EXCLUDED.start_date,
          status = EXCLUDED.status,
          user_id = EXCLUDED.user_id,
          name = EXCLUDED.name,
          email = EXCLUDED.email,
          phone = EXCLUDED.phone,
          search_document = EXCLUDED.search_document
      SQL

      # Log the SQL and values for debugging (only in development)
      if Rails.env.development?
        Rails.logger.debug("SQL for employee #{employee.id}: #{sql}")
        Rails.logger.debug("Status value for employee #{employee.id}: #{status_value}, User ID value: #{user_id_value}")
      end

      # Execute the SQL
      connection.execute(sql)
    end

    # Refresh the materialized view
    refresh
  end

  # Class method to update search data for a specific employee
  def self.update_for_employee(employee)
    # Make sure the base table exists
    unless connection.table_exists?('employee_search_views_data')
      update_base_view_data
      return
    end

    # Get user data if available
    name = nil
    email = nil

    if employee.respond_to?(:user_data) && employee.user_data.present?
      name = employee.name
      email = employee.email
    end

    # Create search document
    search_document = [
      employee.department.to_s,
      employee.department_name.to_s, # Add department name for better search
      name.to_s,
      email.to_s,
      employee.phone.to_s
    ].join(' ')

    # Log the status value for debugging (only in development)
    Rails.logger.debug("Employee status: #{employee.status.inspect}, class: #{employee.status.class}") if Rails.env.development?

    # Handle status value safely using the helper method
    # The status field is an enum (integer) in the database
    status_value = sql_safe_value(employee.status)

    # Handle user_id value safely using the helper method
    # The user_id field is a bigint in the database
    user_id_value = sql_safe_value(employee.user_id)

    # Handle department value safely - get the integer enum value
    # The department field is an enum (integer) in the database
    department_value = Employee.departments[employee.department] || 'NULL'

    # Build the complete SQL statement
    sql = <<-SQL
      INSERT INTO employee_search_views_data (
        employee_id, department, start_date, status, user_id, name, email, phone, search_document
      ) VALUES (
        #{employee.id},
        #{department_value},
        #{connection.quote(employee.start_date)},
        #{status_value},
        #{user_id_value},
        #{connection.quote(name)},
        #{connection.quote(email)},
        #{connection.quote(employee.phone.to_s)},
        #{connection.quote(search_document)}
      )
      ON CONFLICT (employee_id) DO UPDATE SET
        department = EXCLUDED.department,
        start_date = EXCLUDED.start_date,
        status = EXCLUDED.status,
        user_id = EXCLUDED.user_id,
        name = EXCLUDED.name,
        email = EXCLUDED.email,
        phone = EXCLUDED.phone,
        search_document = EXCLUDED.search_document
    SQL

    # Log the SQL and values for debugging (only in development)
    if Rails.env.development?
      Rails.logger.debug("SQL: #{sql}")
      Rails.logger.debug("Status value: #{status_value}, User ID value: #{user_id_value}")
    end

    # Execute the SQL
    connection.execute(sql)

    # Refresh the materialized view
    refresh
  end
end
