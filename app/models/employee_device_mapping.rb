class EmployeeDeviceMapping < ApplicationRecord
  belongs_to :employee
  belongs_to :attendance_device, class_name: 'Attendance::Device'
  
  validates :device_user_id, presence: true
  validates :employee_id, uniqueness: { 
    scope: :attendance_device_id, 
    message: "is already mapped to this device" 
  }
  validates :device_user_id, uniqueness: { 
    scope: :attendance_device_id,
    message: "is already taken on this device" 
  }
  
  # Scopes
  scope :for_device, ->(device) { where(attendance_device: device) }
  scope :for_employee, ->(employee) { where(employee: employee) }
  
  # Class methods
  def self.find_employee_for_device(device, device_user_id)
    mapping = find_by(attendance_device: device, device_user_id: device_user_id)
    mapping&.employee
  end
  
  def self.find_device_user_id_for_employee(employee, device)
    mapping = find_by(employee: employee, attendance_device: device)
    mapping&.device_user_id
  end
  
  # Instance methods
  def device_name
    attendance_device.name
  end
  
  def employee_name
    employee.name
  end
  
  def to_s
    "#{employee_name} → Device #{device_name} (User ID: #{device_user_id})"
  end
end
