module Leave::ApprovalConcern
  extend ActiveSupport::Concern

  included do
    attr_accessor :actor_user_id

    after_create :submit_for_approval_if_pending
    before_save :update_remote_status, if: :withdrawal_requested?

    acts_as_approvable

    def approval_action
      "request_leave"
    end

    def system_name
      "people"
    end

    def on_approval_status_change(new_status, previous_status)
      Rails.logger.info("Leave callback: status changed from #{previous_status} to #{new_status}")
      update!(status: new_status)
    end

    def update_remote_status
      approval_request&.cancel!(actor_user_id)

    rescue StandardError => e
      Rails.logger.error("Failed to update remote status: #{e.message}")
      errors.add(:base, "Failed to update remote status: #{e.message}")
      throw(:abort)
    end

    # Prepare context for approval workflow
    def approval_context
      {
        leave_type: leave_type,
        days: duration.to_s,
        priority: determine_priority
      }
    end

    # Determine priority based on leave type and duration
    def determine_priority
      if leave_type == "sick" && duration > 7
        "high"
      elsif leave_type == "maternity" || leave_type == "paternity"
        "high"
      elsif duration > 14
        "high"
      elsif duration > 7
        "medium"
      else
        "low"
      end
    end

    private

    def submit_for_approval_if_pending
      if pending? && !approval_request
        result = submit_for_approval(employee.user_id, nil, approval_context)

        unless result
          error_message = approval_error || "Failed to create approval request"
          errors.add(:base, error_message)
          throw(:abort)
        end
      end
    end
  end
end
