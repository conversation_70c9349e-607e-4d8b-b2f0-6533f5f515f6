module Salary
  module LeaveIntegration
    extend ActiveSupport::Concern

    def calculate_leave_deductions(calculation)
      # Get period dates
      start_date = calculation.period_start_date
      end_date = calculation.period_end_date

      # Get unpaid leaves for this period
      unpaid_leaves = employee.leaves
                              .approved
                              .where(leave_type: :unpaid)
                              .where("start_date <= ? AND end_date >= ?", end_date, start_date)

      total_deduction = 0

      unpaid_leaves.each do |leave|
        # Calculate days in this period
        leave_start = [ leave.start_date, start_date ].max
        leave_end = [ leave.end_date, end_date ].min

        # Calculate business days (excluding weekends)
        business_days = calculate_working_days(leave_start, leave_end)

        # Adjust for half-days
        if leave.respond_to?(:half_day?) && leave.half_day?
          business_days = business_days * 0.5
        end

        # Calculate daily rate
        working_days_in_month = calculate_working_days(start_date, end_date)
        daily_rate = working_days_in_month > 0 ? calculation.gross_salary / working_days_in_month : 0

        # Calculate deduction
        deduction = daily_rate * business_days
        total_deduction += deduction

        # Track this specific leave deduction
        calculation.calculation_details.build(
          detail_type: 'deduction',
          category: 'leave_unpaid',
          amount: deduction,
          description: "Unpaid leave from #{leave_start} to #{leave_end} (#{business_days} business days)",
          reference: leave
        )
      end

      total_deduction
    end
  end
end
