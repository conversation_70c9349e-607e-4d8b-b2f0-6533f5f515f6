module Salary
  module SocialSecurityIntegration
    extend ActiveSupport::Concern
    
    def calculate_social_security(gross_salary, date)
      config = SocialSecurityConfig.current(date)
      return 0 unless config

      contributory_salary = config.max_salary ? [gross_salary, config.max_salary].min : gross_salary
      contributory_salary * (config.employee_rate / 100.0)
    end

    def calculate_employer_contribution(gross_salary, date)
      config = SocialSecurityConfig.current(date)
      return 0 unless config

      contributory_salary = config.max_salary ? [gross_salary, config.max_salary].min : gross_salary
      contributory_salary * (config.employer_rate / 100.0)
    end
  end
end
