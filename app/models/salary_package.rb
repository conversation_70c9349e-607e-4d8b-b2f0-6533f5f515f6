class SalaryPackage < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable
  include SalaryPackage::Creation

  belongs_to :employee
  has_many :salary_calculations

  validates :base_salary, presence: true, numericality: { greater_than: 0 }
  validates :effective_date, presence: true

  # Scopes
  scope :current, ->(date = Date.current) { where("effective_date <= ? AND (end_date IS NULL OR end_date >= ?)", date, date) }
  scope :future, ->(date = Date.current) { where("effective_date > ?", date) }
  scope :past, ->(date = Date.current) { where("end_date < ?", date) }
  scope :historical, -> { order(effective_date: :desc) }

  # Methods
  def total_package_value
    base_salary + housing_allowance.to_f + transportation_allowance.to_f + other_allowances.to_f
  end

  # Sets an end date for the salary package
  # Used when creating a new package to end the previous one
  # @param end_date [Date] the date when the package ends, defaults to current date
  # @return [Boolean] true if the package was successfully updated, false otherwise
  # @raise [ActiveRecord::RecordInvalid] if the record is invalid
  def set_end_date!(end_date = Date.current)
    if end_date < effective_date
      errors.add(:end_date, "cannot be earlier than effective date")
      return false
    end

    update!(end_date: end_date)
  rescue ActiveRecord::RecordInvalid => e
    errors.add(:base, e.message)
    false
  end

  # Check if this package is currently active
  # @param date [Date] the date to check against, defaults to current date
  # @return [Boolean] true if the package is active on the given date
  def active?(date = Date.current)
    effective_date <= date && (end_date.nil? || end_date >= date)
  end
end
