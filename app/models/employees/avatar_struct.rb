module Employees
  # Avatar struct to provide a clean interface for employee avatar attributes
  # This struct represents the avatar data including the binary image and metadata
  class AvatarStruct
    attr_accessor :image, :filename, :content_type, :url, :bytes_size

    def initialize(attributes = {})
      @image = attributes[:image] || attributes["image"]
      @filename = attributes[:filename] || attributes["filename"]
      @content_type = attributes[:content_type] || attributes["content_type"]
      @bytes_size = attributes[:bytes_size] || attributes["bytes_size"]
      @url = attributes[:url] || attributes["url"]
    end

    # Support for as_json - simply returns the struct as a hash
    def as_json
      result = {}
      result["image"] = image if image.present?
      result["filename"] = filename if filename.present?
      result["content_type"] = content_type if content_type.present?
      result["bytes_size"] = bytes_size if bytes_size.present?
      result["url"] = url if url.present?
      result
    end

    # Convert to hash for JSON serialization
    def to_h
      as_json
    end

    public

    # Allow hash-like access with symbols
    def [](key)
      send(key) if respond_to?(key)
    end

    # Allow hash-like assignment with symbols
    def []=(key, value)
      send("#{key}=", value) if respond_to?("#{key}=")
    end

    # Check if the struct is empty
    def empty?
      image.nil? && filename.nil? && content_type.nil? && url.nil? && bytes_size.nil?
    end

    # Check if the struct has content
    def present?
      !empty?
    end

    # Convert from uploaded file
    def self.from_uploaded_file(file, url = nil)
      return new if file.nil?

      new(
        image: file.respond_to?(:read) ? file.read : file,
        filename: file.respond_to?(:original_filename) ? file.original_filename : nil,
        content_type: file.respond_to?(:content_type) ? file.content_type : nil,
        bytes_size: file.respond_to?(:size) ? file.size : nil,
        url: url
      )
    end
  end
end
