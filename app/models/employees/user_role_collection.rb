# frozen_string_literal: true

module Employees
  class UserRoleCollection
    include Athar::Commons::ActiveStruct::Collection

    # Define the item class for this collection
    collection_item_class UserRole

    # Alias for ids method to match the naming convention
    alias_method :user_role_ids, :ids
    alias_method :user_role_ids=, :ids=

    # Add methods specific to user roles
    def global_roles
      select(&:global?)
    end

    def project_roles
      reject(&:global?)
    end

    def default_role
      find_by(is_default: true)
    end

    def find_by_role_id(role_id)
      find { |user_role| user_role.role&.id.to_s == role_id.to_s }
    end

    def find_by_project_id(project_id)
      select { |user_role| user_role.project&.id.to_s == project_id.to_s }
    end
  end
end
