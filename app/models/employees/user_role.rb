# frozen_string_literal: true

module Employees
  class UserRole < Athar::Commons::ActiveStruct::Base
    # Disable auto-generated ID
    disable_id_generation

    # Basic attributes
    attribute :is_default, :boolean

    # Associations
    # belongs_to :employee  # ActiveRecord model
    belongs_to :project # ActiveStruct object
    belongs_to :role # ActiveStruct object

    # Helper methods
    def global?
      role&.global == true
    end

    def default?
      is_default == true
    end
  end
end
