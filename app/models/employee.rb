class Employee < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable
  include AtharRpc::ActiveRpc

  include Employee::DepartmentConcern
  include Employee::SearchConcern

  # Constants
  DEFAULT_PASSWORD = "Password@123".freeze

  has_many_attached :attachments
  has_many :leaves, dependent: :destroy
  has_many :attendance_events, class_name: 'Attendance::Event', dependent: :destroy
  has_many :attendance_summaries, class_name: 'Attendance::Summary', dependent: :destroy
  has_many :attendance_periods, class_name: 'Attendance::Period', dependent: :destroy
  has_many :salary_packages, dependent: :destroy
  has_many :salary_calculations, dependent: :destroy
  has_one :salary_package, -> { current }, class_name: 'SalaryPackage'

  # Device mapping associations
  has_many :employee_device_mappings, dependent: :destroy
  has_many :mapped_devices, through: :employee_device_mappings, source: :attendance_device

  after_initialize :set_default_password, if: :new_record?

  # Device sync callbacks
  after_create :queue_device_sync_create
  after_update :queue_device_sync_update, if: :sync_relevant_changes?
  before_destroy :queue_device_sync_delete

  # Phone number validation and formatting
  phonofy :phone, phonelib: { countries: [ :jo, :eg ], types: [ :mobile, :fixed_line ] }

  # Configure the gRPC integration
  active_rpc :core, :User, foreign_key: :user_id do
    attribute :name, :string
    attribute :email, :string
    attribute :password, :string
    attribute :roles, :json, default: []
    attribute :user_roles_list, :user_role_collection, default: []
    attribute :permissions, :json, default: []
    attribute :avatar_attributes, :avatar_attributes_type, default: {}

    # Define query configuration
    query_config(
      searchable: [ :name, :email, :status ],
      filterable: [ :name, :email, :status ],
      sortable: [ :name, :email, :status ],
      includable: [ :user_roles, :avatar_attributes ]
    )

    # Define available scopes
    scope :with_role, "Filter users by role name"
    scope :with_permission, "Filter users by permission name"
    scope :in_project, "Filter users by project"
  end

  # Enums
  enum :status, { active: 0, inactive: 1 }

  # Scopes
  scope :exempt_from_attendance_deductions, -> { where(exempt_from_attendance_deductions: true) }
  scope :not_exempt_from_attendance_deductions, -> { where(exempt_from_attendance_deductions: false) }

  # Validations
  validates :phone, presence: true
  validates :start_date, presence: true
  validates :exempt_from_attendance_deductions, inclusion: { in: [true, false] }
  # validates :name, presence: true
  # validates :email, presence: true, uniqueness: true
  # validates :password, presence: true, on: :create
  # validates :user_id, uniqueness: true

  # Calculated fields
  # def role_name
  #   return if roles_list.blank? || roles_list.first.blank?
  #
  #   roles_list.first.to_s.titleize rescue nil
  # end

  def phone_intl
    phone(:international)
  end

  # Method wrapper for handling avatar uploads
  # @param file [ActionDispatch::Http::UploadedFile] The uploaded avatar file
  # @return [Employees::AvatarStruct] The avatar attributes for the remote user
  def avatar=(file)
    return unless file.present?

    self.avatar_attributes = file
  end

  # Get the avatar URL
  # @return [String] The avatar URL
  def avatar_url
    avatar_attributes&.url
  end

  # Method to get or create device code for attendance integration (legacy)
  def ensure_device_code
    return device_code if device_code.present?

    # Generate a device code based on employee ID or other unique identifier
    update(device_code: "EMP#{id.to_s.rjust(4, '0')}")
    device_code
  end

  # Device mapping methods (new multi-device support)

  # Get device code for specific device
  def device_code_for(device)
    mapping = employee_device_mappings.find_by(attendance_device: device)
    mapping&.device_user_id || device_code  # Fallback to legacy device_code
  end

  # Set device code for specific device
  def set_device_code_for(device, user_id, notes: nil)
    mapping = employee_device_mappings.find_or_initialize_by(attendance_device: device)
    mapping.device_user_id = user_id
    mapping.notes = notes if notes.present?
    mapping.save!
    mapping
  end

  # Remove device mapping
  def remove_device_mapping(device)
    employee_device_mappings.where(attendance_device: device).destroy_all
  end

  # Get all device mappings
  def device_mappings
    employee_device_mappings.includes(:attendance_device)
  end

  # Check if employee is mapped to a device
  def mapped_to_device?(device)
    employee_device_mappings.exists?(attendance_device: device)
  end

  # Get all devices this employee is mapped to
  def mapped_device_list
    mapped_devices.pluck(:name, :id)
  end

  # Helper methods for user roles
  def add_role(role_params)
    # Initialize user_roles_list if it's nil
    self.user_roles_list ||= Employees::UserRoleCollection.new(id, :employee)
    self.user_roles_list << role_params
    self.user_roles_list
  end

  # Define a method that returns individual user roles for the serializer
  def user_role_records
    # Ensure we always return an array, even if user_roles_list is nil
    return [] if user_roles_list.nil?
    user_roles_list.to_a
  end

  # Alias for user_roles_list to support the serializer
  def user_roles
    user_roles_list
  end

  # Add user_role_ids method for compatibility with ActiveStruct
  def user_role_ids
    return [] unless user_roles_list

    # Handle both UserRoleCollection and Array cases
    if user_roles_list.respond_to?(:ids)
      user_roles_list.ids
    elsif user_roles_list.is_a?(Array)
      user_roles_list.map { |role| role.is_a?(Hash) ? role['id'] : role&.id }.compact
    else
      []
    end
  end

  # Add user_role_ids= method for compatibility with ActiveStruct
  def user_role_ids=(ids)
    self.user_roles_list ||= Employees::UserRoleCollection.new
    self.user_roles_list.ids = ids
  end

  private

  # Sets the default password if not already set
  def set_default_password
    self.password ||= DEFAULT_PASSWORD
  end

  # Device sync callback methods
  def queue_device_sync_create
    # Queue sync to all mapped devices (or default devices if none mapped)
    devices_to_sync = mapped_devices.any? ? mapped_devices : default_sync_devices

    devices_to_sync.each do |device|
      sync_log = Attendance::SyncLog.create_for_employee_sync(
        device,
        self,
        'create_user',
        nil # triggered_by
      )

      # Queue the worker
      ::Attendance::DeviceSyncWorker.perform_async(device.id, sync_log.id)
    end
  end

  def queue_device_sync_update
    # Queue sync to all mapped devices
    mapped_devices.each do |device|
      sync_log = Attendance::SyncLog.create_for_employee_sync(
        device,
        self,
        'update_user',
        nil # triggered_by
      )

      # Queue the worker
      ::Attendance::DeviceSyncWorker.perform_async(device.id, sync_log.id)
    end
  end

  def queue_device_sync_delete
    # Queue sync to all mapped devices
    mapped_devices.each do |device|
      sync_log = Attendance::SyncLog.create_for_employee_sync(
        device,
        self,
        'delete_user',
        nil # triggered_by
      )

      # Queue the worker
      ::Attendance::DeviceSyncWorker.perform_async(device.id, sync_log.id)
    end
  end

  def sync_relevant_changes?
    # Only sync if name or status changed
    saved_changes.keys.intersect?(['name', 'status'])
  end

  def default_sync_devices
    # Return all active devices if no specific mappings exist
    # You can customize this logic based on your needs
    Attendance::Device.active
  end
end
