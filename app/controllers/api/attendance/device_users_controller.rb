module Api
  module Attendance
    class DeviceUsersController < Athar::Commons::Api::BaseController
      include Athar::Commons::Api::Concerns::Serializable
      include Athar::Commons::Api::Concerns::Paginatable
      include Athar::Commons::Api::Concerns::FilterableSortable

      before_action :authenticate_session!
      before_action :set_device
      before_action :authorize_device_access

      api! "Lists device users stored on the attendance device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "Device ID"
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param :q, String, desc: "Search query for user name or ID"
      param :filter, Hash, desc: "Filter options" do
        param :privilege, Integer, desc: "Filter by privilege level (0=user, 1=admin, etc.)"
        param :enabled, String, desc: "Filter by enabled status (true/false)"
      end
      description <<-HTML
        <b>Endpoint Details</b>

        Returns a list of all users stored directly on the attendance device.
        This endpoint provides pure device data without any employee mapping.
        Supports filtering by name, user_id, privilege level, and enabled status.
        Supports sorting by name, user_id, or privilege.
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      returns code: 200, desc: "List of device users"
      error code: 422, desc: "Device does not support user management"
      error code: 503, desc: "Device communication error"

      def index
        begin
          adapter = @device.create_adapter

          unless adapter.supports_user_management?
            serialize_errors({
                               detail: "Device does not support user management",
                               device_type: @device.adapter_type
                             }, :unprocessable_entity)
            return
          end

          # Get users from device and convert to ActiveStruct collection with Ransack support
          device_user_hashes = adapter.get_users
          @collection = ::Attendance::DeviceUserCollection.from_device_hashes(device_user_hashes)

          # TODO: params casting to be handled in the adapter or the related activestruck,
          apply_filters(@collection) do |filtered_and_sorted|
            # Use custom pagination for ActiveStruct collections
            records, meta = paginate_active_struct_collection(filtered_and_sorted)

            # Add device metadata
            meta[:device] = {
              id: @device.id,
              name: @device.name,
              adapter_type: @device.adapter_type,
              total_users: @collection.size,
              supports_user_management: adapter.supports_user_management?
            }

            serialize_response(records, meta: meta)
          end

        rescue => e
          Rails.logger.error("Failed to get users from device #{@device.name}: #{e.message}")
          serialize_errors({
                             detail: "Failed to retrieve users from device",
                             error: e.message
                           }, :service_unavailable)
        end
      end

      api! "Shows a specific device user (pure device data)"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "Device ID"
      param :id, String, required: true, desc: "Device User ID"
      description <<-HTML
        <b>Endpoint Details</b>

        Returns details for a specific user stored on the attendance device.
        This endpoint provides pure device data without any employee mapping.
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      returns code: 200, desc: "Device user details"
      error code: 404, desc: "User not found on device"
      error code: 503, desc: "Device communication error"

      def show
        begin
          adapter = @device.create_adapter
          device_user_hashes = adapter.get_users
          user_hash = device_user_hashes.find { |u| u[:user_id] == params[:id] }

          unless user_hash
            serialize_errors({ detail: "User not found on device" }, :not_found)
            return
          end

          # Convert to ActiveStruct object (pure device data only)
          device_user = ::Attendance::DeviceUser.from_device_hash(user_hash)

          serialize_response(device_user)

        rescue => e
          Rails.logger.error("Failed to get device user: #{e.message}")
          serialize_errors({
                             detail: "Failed to retrieve device user",
                             error: e.message
                           }, :service_unavailable)
        end
      end

      api! "Updates an existing user on the attendance device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "Device ID"
      param :id, String, required: true, desc: "User ID to update"
      param :user, Hash, required: true, desc: "User data to update" do
        param :name, String, desc: "User name"
        param :privilege, Integer, desc: "User privilege level (0=user, 1=admin, etc.)"
        param :password, String, desc: "User password"
        param :group_id, String, desc: "User group ID"
        param :card_number, Integer, desc: "User card number"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Updates an existing user on the attendance device.
        This endpoint requires the device to support user management.
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "User updated successfully"
      error code: 422, desc: "Device does not support user management or failed to update user"
      error code: 500, desc: "Internal server error"

      def update
        adapter = @device.create_adapter

        unless adapter.supports_user_management?
          render json: {
            error: "Device does not support user management"
          }, status: :unprocessable_entity
          return
        end

        success = adapter.update_user(params[:id], update_user_params.to_h.symbolize_keys)

        if success
          render json: {
            message: "User updated successfully",
            user_id: params[:id]
          }
        else
          render json: {
            error: "Failed to update user on device"
          }, status: :unprocessable_entity
        end
      rescue => e
        Rails.logger.error("Error updating device user: #{e.message}")
        render json: {
          error: "Failed to update device user",
          details: e.message
        }, status: :internal_server_error
      end

      api! "Deletes a user from the attendance device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "Device ID"
      param :id, Integer, required: true, desc: "User ID to delete"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Deletes a user from the attendance device.
        This endpoint requires the device to support user management.
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "User deleted successfully"
      error code: 422, desc: "Device does not support user management or failed to delete user"
      error code: 500, desc: "Internal server error"

      def destroy
        adapter = @device.create_adapter

        unless adapter.supports_user_management?
          render json: {
            error: "Device does not support user management"
          }, status: :unprocessable_entity
          return
        end

        success = adapter.delete_user(params[:id])

        if success
          render json: {
            message: "User deleted successfully",
            user_id: params[:id]
          }
        else
          render json: {
            error: "Failed to delete user from device"
          }, status: :unprocessable_entity
        end
      rescue => e
        Rails.logger.error("Error deleting device user: #{e.message}")
        render json: {
          error: "Failed to delete device user",
          details: e.message
        }, status: :internal_server_error
      end

      # POST /api/attendance/devices/:device_id/sync_users
      api! "Syncs all employees with device codes to the attendance device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "Device ID"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Syncs all employees with device codes to the attendance device.
        This endpoint requires the device to support user management.
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "User sync completed with results"
      error code: 422, desc: "Device does not support user management"
      error code: 500, desc: "Internal server error"

      def sync_users
        adapter = @device.create_adapter

        unless adapter.supports_user_management?
          render json: {
            error: "Device does not support user management"
          }, status: :unprocessable_entity
          return
        end

        # Get all employees with device codes
        employees = Employee.where.not(device_code: nil)

        success_count = 0
        failure_count = 0
        errors = []

        employees.each do |employee|
          begin
            if adapter.sync_user(employee)
              success_count += 1
            else
              failure_count += 1
              errors << "Failed to sync #{employee.name}"
            end
          rescue => e
            failure_count += 1
            errors << "Error syncing #{employee.name}: #{e.message}"
          end
        end

        render json: {
          message: "User sync completed",
          results: {
            total_employees: employees.count,
            success_count: success_count,
            failure_count: failure_count,
            errors: errors
          }
        }
      rescue => e
        Rails.logger.error("Error syncing users to device: #{e.message}")
        render json: {
          error: "Failed to sync users to device",
          details: e.message
        }, status: :internal_server_error
      end

      # POST /api/attendance/devices/:device_id/sync_employee/:employee_id
      api! "Syncs a specific employee to the attendance device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "Device ID"
      param :employee_id, Integer, required: true, desc: "Employee ID to sync"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Syncs a specific employee to the attendance device.
        This endpoint requires the device to support user management.
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "Employee synced successfully"
      error code: 404, desc: "Employee not found"
      error code: 422, desc: "Device does not support user management or failed to sync employee"
      error code: 500, desc: "Internal server error"

      def sync_employee
        adapter = @device.create_adapter

        unless adapter.supports_user_management?
          render json: {
            error: "Device does not support user management"
          }, status: :unprocessable_entity
          return
        end

        employee = Employee.find(params[:employee_id])

        success = adapter.sync_user(employee)

        if success
          render json: {
            message: "Employee synced successfully",
            employee: {
              id: employee.id,
              name: employee.name,
              device_code: employee.device_code
            }
          }
        else
          render json: {
            error: "Failed to sync employee to device"
          }, status: :unprocessable_entity
        end
      rescue ActiveRecord::RecordNotFound
        render json: { error: "Employee not found" }, status: :not_found
      rescue => e
        Rails.logger.error("Error syncing employee to device: #{e.message}")
        render json: {
          error: "Failed to sync employee to device",
          details: e.message
        }, status: :internal_server_error
      end

      private

      def set_device
        @device = ::Attendance::Device.find(params[:device_id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Device not found" }, :not_found)
      end

      def authorize_device_access
        authorize!(:read, :attendance_device)
      end

      # Filtering and sorting configuration for Ransack
      def filterable_fields
        %w[user_id name privilege group_id card_number]
      end

      def sortable_fields
        %w[user_id name privilege group_id card_number]
      end

      def default_sort
        'name'  # Sort by name by default
      end

      def searchable_fields
        %w[name user_id]
      end

      # Custom pagination for ActiveStruct collections that produces Pagy-compatible metadata
      def paginate_active_struct_collection(collection)
        # Extract pagination parameters
        page_number = params.dig(:page, :number)&.to_i || 1
        page_size = params.dig(:page, :size)&.to_i || 20
        page_size = [page_size, 100].min # Max 100 per page

        # Convert collection to array if needed
        items = collection.respond_to?(:to_a) ? collection.to_a : collection

        # Calculate pagination values
        total_count = items.size
        offset = (page_number - 1) * page_size
        paginated_items = items[offset, page_size] || []

        # Calculate from/to values (1-based indexing)
        from = total_count > 0 ? offset + 1 : 0
        to = total_count > 0 ? [offset + paginated_items.size, total_count].min : 0

        # Create Pagy-compatible metadata
        meta = {
          pagination: {
            count: total_count,      # Total number of records
            page: page_number,       # Current page number
            limit: page_size,        # Items per page
            from: from,              # First record number on this page
            to: to                   # Last record number on this page
          }
        }

        [paginated_items, meta]
      end

      # Override to support custom serializer for ActiveStruct objects
      def prepare_serialization_options(resource, options = {})
        serialization_options = super(resource, options)

        # If a custom serializer is specified, add it to the serializer options
        if options[:serializer]
          serialization_options[:serializer_options][:serializer] = options[:serializer]
        end

        serialization_options
      end

      api! "Creates a new user on the attendance device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "Device ID"
      param :user, Hash, required: true, desc: "User data to create" do
        param :user_id, Integer, required: true, desc: "User ID (must be unique)"
        param :name, String, required: true, desc: "User name"
        param :privilege, Integer, desc: "User privilege level (0=user, 1=admin, etc.)"
        param :password, String, desc: "User password"
        param :group_id, String, desc: "User group ID"
        param :card_number, Integer, desc: "User card number"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Creates a new user on the attendance device.
        This endpoint requires the device to support user management.
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      )
      returns code: 201, desc: "User created successfully"
      error code: 422, desc: "Device does not support user management or failed to create user"
      error code: 500, desc: "Internal server error"

      def create
        adapter = @device.create_adapter

        unless adapter.supports_user_management?
          render json: {
            error: "Device does not support user management"
          }, status: :unprocessable_entity
          return
        end

        success = adapter.create_user(create_user_params.to_h.symbolize_keys)

        if success
          render json: {
            message: "User created successfully",
            user_id: create_user_params[:user_id]
          }, status: :created
        else
          render json: {
            error: "Failed to create user on device"
          }, status: :unprocessable_entity
        end
      rescue => e
        Rails.logger.error("Error creating device user: #{e.message}")
        render json: {
          error: "Failed to create device user",
          details: e.message
        }, status: :internal_server_error
      end

      # Parameters for creating a new device user
      def create_user_params
        params.require(:user).permit(:user_id, :name, :privilege, :password, :group_id, :card_number)
      end

      # Parameters for updating an existing device user
      def update_user_params
        params.require(:user).permit(:name, :privilege, :password, :group_id, :card_number)
      end
    end
  end
end
