# frozen_string_literal: true

module Api
  module Attendance
    class DeviceCommandsController < ApplicationController
      include Athar::Commons::Api::Concerns::Serializable
      include Athar::Commons::Api::Concerns::Paginatable
      include Athar::Commons::Api::Concerns::FilterableSortable

      before_action :authenticate_session!
      before_action :set_device
      before_action :authorize_read, only: [:index, :history]
      before_action :authorize_execute, only: [:create]

      api! "Lists available commands for a device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "ID of the attendance device"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Returns a list of commands available for the specified device.
        Commands vary by device type and adapter capabilities.
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "List of available commands"
      error code: 404, desc: "Device not found"

      def index
        commands = ::Attendance::CommandExecutor.available_commands_for_device(@device)

        # For plain hash responses, use render json directly
        # serialize_response is for ActiveRecord/ActiveStruct objects
        render json: {
          device_id: @device.id,
          device_name: @device.name,
          adapter_type: @device.adapter_type,
          supports_commands: @device.create_adapter.supports_commands?,
          available_commands: commands,
          retrieved_at: Time.current.iso8601
        }
      end

      api! "Executes a command on a device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "ID of the attendance device"
      param :command, Hash, required: true, desc: "Command execution parameters" do
        param :name, String, required: true, desc: "Command name to execute"
        param :parameters, Hash, desc: "Command-specific parameters"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Executes a command on the specified device.
        Commands are executed synchronously and return immediate results.
        All command executions are logged for audit purposes.
        Requires permission: <code>:execute_commands, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "Command execution result"
      error code: 400, desc: "Invalid command or parameters"
      error code: 404, desc: "Device not found"

      def create
        command_data = command_params

        # Validate command
        validation = ::Attendance::CommandExecutor.validate_command(
          @device,
          command_data[:name],
          command_data[:parameters]
        )

        unless validation[:valid]
          serialize_errors({ detail: validation[:error] }, :bad_request)
          return
        end

        # Execute command
        result = ::Attendance::CommandExecutor.execute(
          @device,
          command_data[:name],
          command_data[:parameters],
          current_employee
        )

        # For plain hash responses, use render json directly
        render json: {
          device_id: @device.id,
          command_name: command_data[:name],
          parameters: command_data[:parameters],
          result: result.as_json,
          executed_at: Time.current.iso8601,
          executed_by: current_employee&.id
        }, status: :created
      end

      api! "Gets command execution history for a device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "ID of the attendance device"
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param_group :include_params
      param_group :fields_params
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Returns the command execution history for the specified device.
        Supports filtering, sorting, and pagination following JSON:API standards.
        Requires permission: <code>:read, :attendance_device</code>.

        <b>Filtering Examples:</b>
        <code>GET /api/attendance/devices/1/commands/history?filter[command_name]=test_voice</code>
        <code>GET /api/attendance/devices/1/commands/history?filter[status]=completed</code>

        <b>Sorting Examples:</b>
        <code>GET /api/attendance/devices/1/commands/history?sort=-started_at</code>
        <code>GET /api/attendance/devices/1/commands/history?sort=command_name,started_at</code>
      HTML
      )
      returns code: 200, desc: "Command execution history"
      error code: 404, desc: "Device not found"

      def history
        # Set up collection for filtering and sorting
        @collection = @device.command_executions.includes(:executed_by, :device)

        apply_filters(@collection) do |filtered_and_sorted|
          records, meta = paginate(filtered_and_sorted)

          # Add device context to meta
          meta[:device] = {
            id: @device.id,
            name: @device.name,
            adapter_type: @device.adapter_type
          }

          serialize_response(records, serializer: ::Attendance::CommandExecutionSerializer, meta: meta)
        end
      end

      private

      def set_device
        @device = ::Attendance::Device.find(params[:device_id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Attendance device not found" }, :not_found)
      end

      def command_params
        command_data = params.require(:command).permit(:name, parameters: {})
        {
          name: command_data[:name],
          parameters: command_data[:parameters] || {}
        }
      end

      # Authorization methods following API standards
      def authorize_read
        unless can?(:read, :attendance_device)
          render_forbidden("You don't have permission to view device commands")
          false
        end
      end

      def authorize_execute
        unless can?(:execute_commands, :attendance_device)
          render_forbidden("You don't have permission to execute device commands")
          false
        end
      end

      def render_forbidden(message)
        serialize_errors({ detail: message }, :forbidden)
      end

      # Filtering and sorting configuration
      def filterable_fields
        %w[command_name status started_at completed_at]
      end

      def sortable_fields
        %w[command_name status started_at completed_at duration]
      end

      def default_sort
        '-started_at'  # Most recent first
      end

      def searchable_fields
        %w[command_name]
      end
    end
  end
end