# frozen_string_literal: true

module Api
  module Attendance
    class DeviceCommandsController < Athar::Commons::Api::BaseController
      include Athar::Commons::Api::Concerns::Serializable
      include Athar::Commons::Api::Concerns::Paginatable
      include Athar::Commons::Api::Concerns::FilterableSortable

      before_action :set_device
      before_action :authorize_read, only: [:index, :history]
      before_action :authorize_execute, only: [:create]

      api! "Lists available commands for a device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "ID of the attendance device"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Returns a list of commands available for the specified device.
        Commands vary by device type and adapter capabilities.
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "List of available commands"
      error code: 404, desc: "Device not found"

      def index
        commands = Attendance::CommandExecutor.available_commands_for_device(@device)

        render json: {
          device_id: @device.id,
          device_name: @device.name,
          adapter_type: @device.adapter_type,
          supports_commands: @device.create_adapter.supports_commands?,
          available_commands: commands,
          retrieved_at: Time.current.iso8601
        }
      end

      api! "Executes a command on a device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "ID of the attendance device"
      param :command, Hash, required: true, desc: "Command execution parameters" do
        param :name, String, required: true, desc: "Command name to execute"
        param :parameters, Hash, desc: "Command-specific parameters"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Executes a command on the specified device.
        Commands are executed synchronously and return immediate results.
        All command executions are logged for audit purposes.
        Requires permission: <code>:execute_commands, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "Command execution result"
      error code: 400, desc: "Invalid command or parameters"
      error code: 404, desc: "Device not found"

      def create
        command_data = command_params

        # Validate command
        validation = Attendance::CommandExecutor.validate_command(
          @device,
          command_data[:name],
          command_data[:parameters]
        )

        unless validation[:valid]
          render json: { error: validation[:error] }, status: :bad_request
          return
        end

        # Execute command
        result = Attendance::CommandExecutor.execute(
          @device,
          command_data[:name],
          command_data[:parameters],
          current_employee
        )

        render json: {
          device_id: @device.id,
          command_name: command_data[:name],
          parameters: command_data[:parameters],
          result: result.as_json,
          executed_at: Time.current.iso8601,
          executed_by: current_employee&.id
        }
      end

      api! "Gets command execution history for a device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "ID of the attendance device"
      param_group :pagination_params
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Returns the command execution history for the specified device.
        Results are paginated and ordered by execution time (most recent first).
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "Command execution history"
      error code: 404, desc: "Device not found"

      def history
        executions = @device.command_executions
                           .includes(:executed_by)
                           .recent
                           .limit(50)

        render json: {
          device_id: @device.id,
          device_name: @device.name,
          total_executions: @device.command_executions.count,
          recent_executions: executions.map do |execution|
            {
              id: execution.id,
              command_name: execution.command_name,
              parameters: execution.parameters,
              status: execution.status,
              result: execution.result,
              executed_at: execution.started_at&.iso8601,
              completed_at: execution.completed_at&.iso8601,
              duration: execution.duration,
              executed_by: execution.executed_by ? {
                id: execution.executed_by.id,
                name: execution.executed_by.user&.name
              } : nil
            }
          end,
          retrieved_at: Time.current.iso8601
        }
      end

      private

      def set_device
        @device = ::Attendance::Device.find(params[:device_id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Attendance device not found" }, :not_found)
      end

      def command_params
        command_data = params.require(:command).permit(:name, parameters: {})
        {
          name: command_data[:name],
          parameters: command_data[:parameters] || {}
        }
      end

      # Authorization methods
      def authorize_read
        authorize!(:read, :attendance_device)
      end

      def authorize_execute
        authorize!(:execute_commands, :attendance_device)
      end
    end
  end
end