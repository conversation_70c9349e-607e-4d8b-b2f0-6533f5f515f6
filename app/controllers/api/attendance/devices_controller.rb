module Api
  module Attendance
    class DevicesController < ApplicationController
      before_action :authenticate_session!
      before_action :set_attendance_device, only: [ :show, :update, :destroy, :test_connection, :sync, :device_info ]
      before_action :set_collection, only: [ :index ]
      before_action :authorize_read, only: [ :index, :health_report ]
      before_action :authorize_read_specific, only: [ :show ]
      before_action :authorize_create, only: [ :create ]
      before_action :authorize_update, only: [ :update ]
      before_action :authorize_destroy, only: [ :destroy ]
      before_action :authorize_test_connection, only: [ :test_connection ]
      before_action :authorize_sync, only: [ :sync ]
      before_action :authorize_multi_sync, only: [ :multi_sync ]

      api! "Lists all attendance devices"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param :status, String, desc: "Filter by status (active, inactive, maintenance, error)"
      param :adapter_type, String, desc: "Filter by adapter type"
      param :location, String, desc: "Filter by location"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Lists all attendance devices with filtering options.
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "List of attendance devices"

      def index
        apply_filters(@collection) do |filtered_and_sorted|
          records, meta = paginate(filtered_and_sorted)
          serialize_response(records, meta: meta, serializer: ::Attendance::DeviceSerializer)
        end
      end

      api! "Shows a specific attendance device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the attendance device"
      param_group :include_params
      param_group :fields_params
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Shows details for a specific attendance device.
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "Attendance device details"
      error code: 404, desc: "Attendance device not found"

      def show
        serialize_response(@attendance_device, serializer: ::Attendance::DeviceSerializer)
      end

      api! "Creates a new attendance device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :attendance_device, Hash, required: true, desc: "Attendance device attributes" do
        param :name, String, required: true, desc: "Device name"
        param :adapter_type, String, required: true, desc: "Adapter type (zkteco, generic_http, file_import, or any custom adapter)"
        param :ip_address, String, desc: "Device IP address (required for network devices)"
        param :port, Integer, desc: "Device port"
        param :location, String, desc: "Device location"
        param :status, String, desc: "Device status"
        param :connection_config, Hash, desc: "Connection configuration (JSONB)"
        param :sync_config, Hash, desc: "Sync configuration (JSONB)"
        param :capabilities, Hash, desc: "Device capabilities (JSONB)"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Creates a new attendance device.
        Requires permission: <code>:create, :attendance_device</code>.
      HTML
      )
      returns code: 201, desc: "Attendance device created successfully"
      error code: 422, desc: "Validation errors"

      def create
        @attendance_device = ::Attendance::Device.new(attendance_device_params)

        if @attendance_device.save
          serialize_response(@attendance_device, status: :created, serializer: ::Attendance::DeviceSerializer)
        else
          serialize_errors(@attendance_device.errors)
        end
      end

      api! "Updates an attendance device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the attendance device"
      param :attendance_device, Hash, required: true, desc: "Attendance device attributes" do
        param :name, String, desc: "Device name"
        param :adapter_type, String, desc: "Adapter type"
        param :ip_address, String, desc: "Device IP address"
        param :port, Integer, desc: "Device port"
        param :location, String, desc: "Device location"
        param :status, String, desc: "Device status"
        param :connection_config, Hash, desc: "Connection configuration (JSONB)"
        param :sync_config, Hash, desc: "Sync configuration (JSONB)"
        param :capabilities, Hash, desc: "Device capabilities (JSONB)"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Updates an existing attendance device.
        Requires permission: <code>:update, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "Attendance device updated successfully"
      error code: 422, desc: "Validation errors"

      def update
        if @attendance_device.update(attendance_device_params)
          serialize_response(@attendance_device, serializer: ::Attendance::DeviceSerializer)
        else
          serialize_errors(@attendance_device.errors)
        end
      end

      api! "Deletes an attendance device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the attendance device"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Deletes an attendance device by ID.
        Requires permission: <code>:destroy, :attendance_device</code>.
      HTML
      )
      returns code: 204, desc: "Attendance device deleted successfully"

      def destroy
        @attendance_device.destroy!
        head :no_content
      end

      api! "Tests device connection"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the attendance device"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Tests the connection to an attendance device.
        Requires permission: <code>:test_connection, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "Connection test result"

      def test_connection
        result = @attendance_device.test_connection

        render json: {
          device_id: @attendance_device.id,
          device_name: @attendance_device.name,
          connection_successful: result,
          tested_at: Time.current.iso8601,
          message: result ? 'Connection successful' : 'Connection failed'
        }
      end

      api! "Syncs device data"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the attendance device"
      param :sync, Hash, desc: "Sync parameters" do
        param :start_date, Date, desc: "Start date (defaults to yesterday if not provided)"
        param :end_date, Date, desc: "End date (defaults to today if not provided)"
        param :sync_type, String, desc: "Sync type (manual, scheduled, real_time, retry)", default: 'manual'
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Initiates a sync operation for an attendance device.
        Requires permission: <code>:sync, :attendance_device</code>.
      HTML
      )
      returns code: 202, desc: "Sync job queued successfully"

      def sync
        sync_data = sync_params

        # Create sync log
        sync_log = ::Attendance::SyncLog.create_for_sync(
          @attendance_device,
          sync_data[:sync_type],
          { start_date: sync_data[:start_date], end_date: sync_data[:end_date] }
        )

        # Queue background job
        ::Attendance::DeviceSyncWorker.perform_async(@attendance_device.id, sync_log.id)

        render json: {
          device_id: @attendance_device.id,
          sync_log_id: sync_log.id,
          status: 'queued',
          message: 'Sync job queued successfully',
          sync_parameters: sync_data
        }, status: :accepted
      end

      api! "Gets device information"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the attendance device"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Retrieves detailed information from an attendance device.
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "Device information"

      def device_info
        device_info = @attendance_device.get_device_info

        render json: {
          device_id: @attendance_device.id,
          device_name: @attendance_device.name,
          adapter_type: @attendance_device.adapter_type,
          device_info: device_info,
          retrieved_at: Time.current.iso8601
        }
      end

      api! "Syncs multiple devices"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :devices, Hash, required: true, desc: "Multi-device sync parameters" do
        param :device_ids, Array, of: Integer, desc: "Array of device IDs"
        param :start_date, Date, desc: "Start date (defaults to yesterday if not provided)"
        param :end_date, Date, desc: "End date (defaults to today if not provided)"
        param :sync_type, String, desc: "Sync type", default: 'manual'
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Initiates sync operations for multiple devices.
        Requires permission: <code>:sync, :attendance_device</code>.
      HTML
      )
      returns code: 202, desc: "Multi-device sync job queued successfully"

      def multi_sync
        sync_data = multi_sync_params

        # Validate devices exist and user has access
        devices = ::Attendance::Device.where(id: sync_data[:device_ids])
        # Authorization is already handled by authorize_multi_sync before_action

        # Queue multi-device sync job
        ::AttendanceMultiDeviceSyncWorker.perform_async(sync_data)

        render json: {
          device_count: devices.count,
          device_ids: sync_data[:device_ids],
          status: 'queued',
          message: 'Multi-device sync job queued successfully',
          sync_parameters: {
            start_date: sync_data[:start_date],
            end_date: sync_data[:end_date],
            sync_type: sync_data[:sync_type]
          }
        }, status: :accepted
      end

      api! "Gets devices health report"
      header "Authorization", "Scoped session token as Bearer token", required: true
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Generates a health report for all attendance devices.
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "Devices health report"

      def health_report
        devices = ::Attendance::Device.all

        health_data = {
          total_devices: devices.count,
          active_devices: devices.active.count,
          device_status_breakdown: devices.group(:status).count,
          adapter_type_breakdown: devices.group(:adapter_type).count,
          devices_with_recent_activity: devices.joins(:attendance_sync_logs)
                                               .where('attendance_sync_logs.created_at > ?', 24.hours.ago)
                                               .distinct.count,
          generated_at: Time.current.iso8601
        }

        render json: health_data
      end

      private

      def set_attendance_device
        @attendance_device = ::Attendance::Device.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Attendance device not found" }, :not_found)
      end

      def set_collection
        @collection = ::Attendance::Device.all
      end

      def attendance_device_params
        params.require(:attendance_device).permit(
          :name, :adapter_type, :ip_address, :port, :location, :status,
          connection_config: {},
          sync_config: {},
          capabilities: {}
        )
      end

      def sync_params
        params.fetch(:sync, {}).permit(:start_date, :end_date, :sync_type)
      end

      def multi_sync_params
        devices_data = params.require(:devices).permit(:start_date, :end_date, :sync_type, device_ids: [])

        # Apply defaults using Apipie's type casting
        {
          device_ids: devices_data[:device_ids] || [],
          start_date: devices_data[:start_date] || Date.yesterday,
          end_date: devices_data[:end_date] || Date.today,
          sync_type: devices_data[:sync_type] || 'manual'
        }
      end

      # Authorization methods
      def authorize_read
        authorize!(:read, :attendance_device)
      end

      def authorize_read_specific
        authorize!(:read, :attendance_device)
      end

      def authorize_create
        authorize!(:create, :attendance_device)
      end

      def authorize_update
        authorize!(:update, :attendance_device)
      end

      def authorize_destroy
        authorize!(:destroy, :attendance_device)
      end

      def authorize_test_connection
        authorize!(:test_connection, :attendance_device)
      end

      def authorize_sync
        authorize!(:sync, :attendance_device)
      end

      def authorize_multi_sync
        authorize!(:sync, :attendance_device)
      end
    end
  end
end




