# frozen_string_literal: true

module Statistics
  module Calculators
    class AverageDailyWorkHoursCalculator
      include BaseCalculator
      include Statistics::Helpers::DateHelpers
      include Statistics::Helpers::EmployeeHelpers
      include Statistics::Helpers::CardHelpers

      def validate_context(context)
        # This calculator can work with or without an employee_id
        # If employee_id is provided, it will calculate for that employee
        # Otherwise, it will calculate for all employees

        if context[:employee_id].present?
          context[:employee] = find_employee(context[:employee_id])
        end

        context[:start_date] = parse_date_with_formats(context[:start_date]) || Date.today.beginning_of_month
        context[:end_date] = parse_date_with_formats(context[:end_date]) || Date.today

        if context[:end_date] < context[:start_date]
          raise ArgumentError, "End date cannot be before start date"
        end

        context[:comparison_period] = (context[:comparison_period] || 'previous').to_sym
        context[:comparison_text] = generate_comparison_text(context[:comparison_period])
      rescue ArgumentError => e
        raise ArgumentError, e.message
      rescue => e
        raise ArgumentError, "Invalid date format: #{e.message}"
      end

      def perform_calculation(context)
        start_date = context[:start_date]
        end_date = context[:end_date]
        comparison_period = context[:comparison_period]

        # Calculate current period
        current_avg_hours = calculate_average_daily_hours(context[:employee], start_date, end_date)

        # Calculate previous period
        prev_start_date, prev_end_date = calculate_previous_period(start_date, end_date, comparison_period)
        previous_avg_hours = calculate_average_daily_hours(context[:employee], prev_start_date, prev_end_date)

        # Generate comparison text
        comparison_text = context[:comparison_text] || generate_comparison_text(comparison_period)

        # Create and return the card
        create_metric_card(
          card_id,
          'Average Daily Work Hours',
          current_avg_hours.round(1),
          'hours',
          current_avg_hours,
          previous_avg_hours,
          comparison_text
        )
      end

      private

      def calculate_average_daily_hours(employee, start_date, end_date)
        # Count workdays in the period (excluding weekends)
        workdays = (start_date..end_date).reject { |d| [ 0, 6 ].include?(d.wday) }.count
        return 0 if workdays.zero?

        # Calculate total work hours
        if employee.present?
          # For a specific employee
          total_minutes = employee.attendance_periods
                                  .where(date: start_date..end_date)
                                  .where(period_type: Attendance::Period::PERIOD_TYPES[:work])
                                  .sum(:duration_minutes)
        else
          # For all employees
          total_minutes = Attendance::Period
                            .where(date: start_date..end_date)
                            .where(period_type: Attendance::Period::PERIOD_TYPES[:work])
                            .sum(:duration_minutes)
        end

        # Calculate average daily hours
        (total_minutes / 60.0) / workdays
      end
    end
  end
end
