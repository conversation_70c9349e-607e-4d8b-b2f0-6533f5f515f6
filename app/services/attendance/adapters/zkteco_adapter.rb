require 'rbzk'

module Attendance
  module Adapters
    class ZktecoAdapter < BaseAdapter
      attr_reader :device_ip, :device_port, :password, :timeout

      def initialize(options = {})
        # Set instance variables first before calling validation
        @device_name = options[:device_name] || 'Unknown Device'
        @device_id = options[:device_id]
        @options = options

        @device_ip = options[:device_ip] || ENV['ZKTECO_DEVICE_IP']
        @device_port = options[:device_port] || ENV['ZKTECO_DEVICE_PORT'] || 4370
        @password = options[:password] || ENV['ZKTECO_DEVICE_PASSWORD'] || 0
        @timeout = options[:timeout] || 10

        # Now validate after everything is set up
        validate_required_options!
      end

      # Fetch attendance data from ZKTeco device
      def fetch_data(start_date = Date.yesterday, end_date = Date.today)
        start_date, end_date = validate_date_range(start_date, end_date)
        log_info("Fetching attendance data for #{start_date} to #{end_date}")

        measure_performance("fetch_data") do
          records = []

          with_connection do |zk_client|
            # Convert dates to timestamps for faster comparison
            start_timestamp = start_date.beginning_of_day.to_i
            end_timestamp = end_date.end_of_day.to_i

            # Get all attendance records
            attendance_logs = zk_client.get_attendance_logs
            log_debug("Retrieved #{attendance_logs.count} total records from device")

            # Optimized filtering using timestamps instead of date objects
            attendance_logs.each do |log|
              log_timestamp = log.timestamp.to_i
              next if log_timestamp < start_timestamp || log_timestamp > end_timestamp

              # Transform ZK attendance log to our standard format
              transformed_record = transform_attendance_record(log)
              records << transformed_record if transformed_record
            end
          end

          log_info("Retrieved #{records.count} attendance records")
          records
        end
      rescue => e
        log_error("Error fetching attendance data", e)
        []
      end

      # Test connection to the device
      def test_connection
        log_info("Testing connection at #{device_ip}:#{device_port}")

        measure_performance("test_connection") do
          with_connection do |zk_client|
            # Try to get basic device info to verify connection
            firmware = zk_client.get_firmware_version
            log_info("Successfully connected. Firmware: #{firmware}")
            true
          end
        end
      rescue => e
        handle_connection_error(e)
      end

      # Capability methods
      def supports_real_time?
        true
      end

      def supports_clear_data?
        true
      end

      # Get device information for diagnostics
      def get_device_info
        with_connection do |zk_client|
          # Read device sizes to get user and record counts
          zk_client.read_sizes

          {
            firmware_version: zk_client.get_firmware_version,
            platform: zk_client.get_platform,
            device_name: zk_client.get_device_name,
            serial_number: zk_client.get_serialnumber,
            users_count: zk_client.instance_variable_get(:@users),
            records_count: zk_client.instance_variable_get(:@records)
          }
        end
      rescue => e
        Rails.logger.error("Error getting device info from #{device_name}: #{e.message}")
        {}
      end

      # Clear attendance records from a device
      def clear_attendance_logs
        with_connection do |zk_client|
          # Note: The rbzk gem doesn't have a clear_attendance method
          # This would need to be implemented if clearing is required
          Rails.logger.warn("Clear attendance logs not implemented for rbzk gem")
          false
        end
      rescue => e
        Rails.logger.error("Error clearing attendance logs from #{device_name}: #{e.message}")
        false
      end

      # User Management Methods

      # Get all users from the device
      def get_users
        log_info("Retrieving users from device")

        with_connection do |zk_client|
          users = zk_client.get_users
          log_info("Retrieved #{users.count} users from device")

          # Transform to standard format
          users.map do |user|
            {
              user_id: user.uid.to_s,
              name: user.name,
              privilege: user.privilege,
              password: user.password,
              group_id: user.group_id,
              card_number: user.card
            }
          end
        end
      rescue => e
        log_error("Error retrieving users from device", e)
        []
      end

      # Create a new user on the device
      def create_user(user_data)
        log_info("Creating user on device: #{user_data[:name]}")

        with_connection do |zk_client|
          # Set the user on the device
          result = zk_client.set_user(
            uid: user_data[:user_id],
            name: user_data[:name] || "",
            privilege: user_data[:privilege] || 0,
            password: user_data[:password] || "",
            group_id: user_data[:group_id] || "",
            user_id: "",
            card: user_data[:card_number]
          )

          log_info("User created successfully: #{user_data[:name]} (#{user_data[:user_id]})")
          true
        end
      rescue => e
        log_error("Error creating user on device", e)
        false
      end

      # Update an existing user on the device
      def update_user(user_id, user_data)
        log_info("Updating user #{user_id} on device")

        with_connection do |zk_client|
          # The rbzk gem doesn't have a direct update_user method
          # This would need to be implemented using the low-level commands
          log_warn("User update not yet implemented for rbzk gem")
          false
        end
      rescue => e
        log_error("Error updating user on device", e)
        false
      end

      # Delete a user from the device
      def delete_user(user_id)
        log_info("Deleting user #{user_id} from device")

        with_connection do |zk_client|
          # Delete the user from the device
          result = zk_client.delete_user(uid: user_id)

          log_info("User deleted successfully: #{user_id}")
          true
        end
      rescue => e
        log_error("Error deleting user from device", e)
        false
      end

      # Sync an employee to the device
      def sync_user(employee, device_user_id = nil)
        log_info("Syncing employee #{employee.name} to device")

        # Use provided device_user_id or fallback to employee ID
        user_id = device_user_id || employee.id.to_s

        user_data = {
          user_id: user_id,
          name: employee.name,
          privilege: 0, # Regular user
          password: nil,
          group_id: "1",
          card_number: nil
        }

        create_user(user_data)
      rescue => e
        log_error("Error syncing employee to device", e)
        false
      end

      # Check if the adapter supports user management
      def supports_user_management?
        true
      end

      # Command Interface Implementation

      def supports_commands?
        true
      end

      def available_commands
        %w[
          restart test_voice clear_logs clear_lcd write_lcd
          unlock door_state poweroff refresh
        ]
      end

      # Command implementations

      def execute_restart(params = {})
        log_info("Executing restart command on device")

        with_connection do |zk_client|
          if zk_client.respond_to?(:restart)
            result = zk_client.restart
            log_info("Device restart command sent successfully")
            Attendance::CommandResult.success("Device restarting")
          else
            log_warn("Restart command not supported by the current rbzk version")
            Attendance::CommandResult.failure("Operation not supported by the current device library")
          end
        end
      rescue => e
        log_error("Error restarting device", e)
        Attendance::CommandResult.failure(e.message)
      end

      def execute_test_voice(params = {})
        log_info("Executing test voice command on device")

        with_connection do |zk_client|
          if zk_client.respond_to?(:test_voice)
            result = zk_client.test_voice
            log_info("Voice test command sent successfully")
            Attendance::CommandResult.success("Voice test completed")
          else
            log_warn("Test voice command not supported by the current rbzk version")
            Attendance::CommandResult.failure("Operation not supported by the current device library")
          end
        end
      rescue => e
        log_error("Error testing voice", e)
        Attendance::CommandResult.failure(e.message)
      end

      def execute_clear_logs(params = {})
        log_info("Executing clear logs command on device")

        with_connection do |zk_client|
          if zk_client.respond_to?(:clear_data)
            result = zk_client.clear_data
            log_info("Logs cleared successfully")
            Attendance::CommandResult.success("Logs cleared successfully")
          else
            log_warn("Clear logs command not supported by the current rbzk version")
            Attendance::CommandResult.failure("Operation not supported by the current device library")
          end
        end
      rescue => e
        log_error("Error clearing logs", e)
        Attendance::CommandResult.failure(e.message)
      end

      def execute_clear_lcd(params = {})
        log_info("Executing clear LCD command on device")

        with_connection do |zk_client|
          if zk_client.respond_to?(:clear_lcd)
            result = zk_client.clear_lcd
            log_info("LCD cleared successfully")
            Attendance::CommandResult.success("LCD display cleared")
          else
            log_warn("Clear LCD command not supported by the current rbzk version")
            Attendance::CommandResult.failure("Operation not supported by the current device library")
          end
        end
      rescue => e
        log_error("Error clearing LCD", e)
        Attendance::CommandResult.failure(e.message)
      end

      def execute_write_lcd(params = {})
        message = params[:message] || "Hello from Athar EMS"
        log_info("Executing write LCD command on device: #{message}")

        with_connection do |zk_client|
          if zk_client.respond_to?(:write_lcd)
            result = zk_client.write_lcd(message)
            log_info("Message written to LCD successfully")
            Attendance::CommandResult.success("Message written to LCD: #{message}")
          else
            log_warn("Write LCD command not supported by the current rbzk version")
            Attendance::CommandResult.failure("Operation not supported by the current device library")
          end
        end
      rescue => e
        log_error("Error writing to LCD", e)
        Attendance::CommandResult.failure(e.message)
      end

      def execute_unlock(params = {})
        log_info("Executing unlock command on device")

        with_connection do |zk_client|
          if zk_client.respond_to?(:unlock_door)
            result = zk_client.unlock_door
            log_info("Door unlocked successfully")
            Attendance::CommandResult.success("Door unlocked")
          else
            log_warn("Unlock command not supported by the current rbzk version")
            Attendance::CommandResult.failure("Operation not supported by the current device library")
          end
        end
      rescue => e
        log_error("Error unlocking door", e)
        Attendance::CommandResult.failure(e.message)
      end

      def execute_door_state(params = {})
        log_info("Executing door state command on device")

        with_connection do |zk_client|
          if zk_client.respond_to?(:get_door_state)
            state = zk_client.get_door_state
            log_info("Door state retrieved successfully")
            Attendance::CommandResult.success("Door state retrieved", { door_state: state })
          else
            log_warn("Door state command not supported by the current rbzk version")
            Attendance::CommandResult.failure("Operation not supported by the current device library")
          end
        end
      rescue => e
        log_error("Error getting door state", e)
        Attendance::CommandResult.failure(e.message)
      end

      def execute_poweroff(params = {})
        log_info("Executing poweroff command on device")

        with_connection do |zk_client|
          if zk_client.respond_to?(:poweroff)
            result = zk_client.poweroff
            log_info("Device poweroff command sent successfully")
            Attendance::CommandResult.success("Device powering off")
          else
            log_warn("Poweroff command not supported by the current rbzk version")
            Attendance::CommandResult.failure("Operation not supported by the current device library")
          end
        end
      rescue => e
        log_error("Error powering off device", e)
        Attendance::CommandResult.failure(e.message)
      end

      def execute_refresh(params = {})
        log_info("Executing refresh command on device")

        with_connection do |zk_client|
          if zk_client.respond_to?(:refresh_data)
            result = zk_client.refresh_data
            log_info("Device data refreshed successfully")
            Attendance::CommandResult.success("Device data refreshed")
          else
            log_warn("Refresh command not supported by the current rbzk version")
            Attendance::CommandResult.failure("Operation not supported by the current device library")
          end
        end
      rescue => e
        log_error("Error refreshing device data", e)
        Attendance::CommandResult.failure(e.message)
      end

      protected

      # Implement base class abstract methods
      def extract_employee_code(zk_log)
        validate_employee_code(zk_log.user_id)
      end

      def extract_timestamp(zk_log)
        standardize_timestamp(zk_log.timestamp)
      end

      def extract_status(zk_log)
        map_status_to_standard(zk_log.status)
      end

      def validate_required_options!
        if device_ip.blank?
          raise ArgumentError, "device_ip is required for ZKTeco adapter. Current options: #{options.inspect}"
        end
        port_int = device_port.to_i
        unless port_int.between?(1, 65535)
          raise ArgumentError, "device_port must be a valid port number. Current port: #{device_port.inspect}"
        end
      end

      private

      # Create a new ZK client instance
      def create_client
        ::RBZK::ZK.new(
          device_ip,
          port: device_port,
          timeout: timeout,
          password: password
        )
      end

      # Execute a block with a connected ZK client
      def with_connection
        zk_client = create_client
        zk_client.connect

        begin
          yield zk_client
        ensure
          zk_client.disconnect if zk_client.connected?
        end
      end

      # Override base transform method to handle ZK-specific data
      def transform_attendance_record(zk_log)
        employee_code = extract_employee_code(zk_log)
        timestamp = extract_timestamp(zk_log)

        return nil if employee_code.blank? || timestamp.blank?

        # Get punch type if available, otherwise default to 0
        punch_value = zk_log.respond_to?(:punch) ? zk_log.punch : 0

        {
          employee_code: employee_code,
          timestamp: timestamp,
          status: extract_status(zk_log),
          location: device_name,
          source_device_id: device_id,
          punch_type: punch_value,
          raw_data: {
            user_id: zk_log.user_id,
            timestamp: zk_log.timestamp,
            status: zk_log.status,
            punch: punch_value
          }
        }
      end
    end
  end
end
