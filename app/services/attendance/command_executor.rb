module Attendance
  class CommandExecutor
    def self.execute(device, command_name, parameters = {}, employee = nil)
      # Create execution record
      execution = CommandExecution.create!(
        device: device,
        command_name: command_name,
        parameters: parameters,
        status: :running,
        executed_by: employee,
        started_at: Time.current
      )

      begin
        # Execute command on adapter
        adapter = device.create_adapter

        # Check if adapter supports commands and the specific command
        unless adapter.supports_commands?
          result = Attendance::CommandResult.failure("Device does not support commands")
          execution.update!(
            status: :failed,
            result: result.as_json,
            completed_at: Time.current
          )
          return result
        end

        unless adapter.available_commands.include?(command_name.to_s)
          result = Attendance::CommandResult.failure("Command '#{command_name}' not supported")
          execution.update!(
            status: :failed,
            result: result.as_json,
            completed_at: Time.current
          )
          return result
        end

        # Execute command
        result = adapter.execute_command(command_name, parameters)

        # Update execution record
        execution.update!(
          status: result.success ? :completed : :failed,
          result: result.as_json,
          completed_at: Time.current
        )

        result
      rescue => e
        result = Attendance::CommandResult.failure(e.message)
        execution.update!(
          status: :failed,
          result: result.as_json,
          completed_at: Time.current
        )

        result
      end
    end
  end
end

