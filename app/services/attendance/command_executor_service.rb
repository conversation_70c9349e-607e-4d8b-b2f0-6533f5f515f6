module Attendance
  class CommandExecutorService
    attr_reader :device, :current_user

    def initialize(device, current_user = nil)
      @device = device
      @current_user = current_user
    end

    def execute(command_name, params = {})
      adapter = device.adapter
      unless adapter.supports_command?(command_name)
        return { success: false, message: "Command '#{command_name}' not supported by device '#{device.name}'." }
      end

      execution_record = create_execution_record(command_name, params)

      begin
        # Log intent to execute
        Rails.logger.info(
          "Executing command '#{command_name}' on device '#{device.name}' (ID: #{device.id}) " \
          "for user '#{current_user&.email || 'System'}' with params: #{params.inspect}"
        )

        execution_record.update!(status: 'in_progress', executed_at: Time.current)

        # Execute the command via the adapter
        # Ensure params are passed as keyword arguments if the adapter expects them that way
        result = adapter.execute_command(command_name, **symbolize_params(params))

        if result[:success]
          execution_record.update!(
            status: 'success',
            result: result[:data]&.to_json, # Store structured data if available
            completed_at: Time.current,
            error_message: nil
          )
          Rails.logger.info(
            "Command '#{command_name}' on device '#{device.name}' (ID: #{device.id}) executed successfully. " \
            "Result: #{result[:message]}"
          )
        else
          execution_record.update!(
            status: 'failed',
            error_message: result[:message],
            completed_at: Time.current
          )
          Rails.logger.warn(
            "Command '#{command_name}' on device '#{device.name}' (ID: #{device.id}) failed. " \
            "Error: #{result[:message]}"
          )
        end
        result # Return the adapter's result
      rescue NotImplementedError => e
        handle_execution_error(execution_record, e, "Command not implemented in adapter")
        { success: false, message: e.message }
      rescue => e
        handle_execution_error(execution_record, e, "Unexpected error during command execution")
        { success: false, message: "An unexpected error occurred: #{e.message}" }
      end
    end

    def get_available_commands
      device.adapter.available_commands
    rescue => e
      Rails.logger.error("Error getting available commands for device '#{device.name}': #{e.message}")
      [] # Return empty if there's an issue instantiating or querying adapter
    end

    def get_command_history(page: 1, per_page: 20)
      Attendance::DeviceCommandExecution.for_device(device.id)
                                      .ordered_by_execution_date
                                      .page(page).per(per_page)
    end

    private

    def create_execution_record(command_name, params)
      Attendance::DeviceCommandExecution.create!(
        device: device,
        command_name: command_name,
        parameters: params,
        status: 'pending',
        executed_by: current_user
      )
    end

    def handle_execution_error(execution_record, error, log_prefix)
      Rails.logger.error("#{log_prefix} for command '#{execution_record.command_name}' on device '#{device.name}': #{error.message}")
      Rails.logger.error(error.backtrace.join("\n"))
      execution_record.update!(
        status: 'failed',
        error_message: error.message,
        completed_at: Time.current
      )
    end

    def symbolize_params(params)
      params.to_h.deep_symbolize_keys
    end
  end
end

