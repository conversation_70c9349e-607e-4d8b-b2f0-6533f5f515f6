module Attendance
  class EmployeeDeviceMappingService
    def self.create_mappings_from_device(device)
      adapter = device.create_adapter
      device_users = adapter.get_users
      
      created_count = 0
      updated_count = 0
      errors = []
      
      device_users.each do |device_user|
        begin
          # Try to find employee by name (you can customize this logic)
          employee = find_employee_for_device_user(device_user)
          
          if employee
            mapping = EmployeeDeviceMapping.find_or_initialize_by(
              employee: employee,
              attendance_device: device
            )
            
            if mapping.persisted?
              # Update existing mapping
              mapping.update!(device_user_id: device_user[:user_id])
              updated_count += 1
            else
              # Create new mapping
              mapping.device_user_id = device_user[:user_id]
              mapping.notes = "Auto-created from device users"
              mapping.save!
              created_count += 1
            end
          else
            errors << "No employee found for device user: #{device_user[:name]} (ID: #{device_user[:user_id]})"
          end
        rescue => e
          errors << "Error processing device user #{device_user[:name]}: #{e.message}"
        end
      end
      
      {
        created_count: created_count,
        updated_count: updated_count,
        total_device_users: device_users.count,
        errors: errors
      }
    end
    
    def self.create_mapping_for_employee(employee, device, device_user_id, notes: nil)
      mapping = EmployeeDeviceMapping.find_or_initialize_by(
        employee: employee,
        attendance_device: device
      )
      
      mapping.device_user_id = device_user_id
      mapping.notes = notes if notes.present?
      mapping.save!
      
      mapping
    end
    
    private
    
    def self.find_employee_for_device_user(device_user)
      # Strategy 1: Exact name match
      employee = Employee.find_by(name: device_user[:name])
      return employee if employee
      
      # Strategy 2: Case-insensitive name match
      employee = Employee.where("LOWER(name) = ?", device_user[:name].downcase).first
      return employee if employee
      
      # Strategy 3: Partial name match (first word)
      first_name = device_user[:name].split(/[,\s]/).first
      employee = Employee.where("LOWER(name) LIKE ?", "%#{first_name.downcase}%").first
      return employee if employee
      
      # No match found
      nil
    end
  end
end
