module Salary
  class SlipService
    attr_reader :salary_calculation

    def initialize(salary_calculation)
      @salary_calculation = salary_calculation
    end

    def generate
      return false unless salary_calculation.paid?

      # Generate PDF using Prawn
      pdf_content = generate_pdf

      # Attach PDF to salary calculation
      attach_pdf(pdf_content)
    end

    private

    def generate_pdf
      require 'prawn'

      employee = salary_calculation.employee

      pdf = Prawn::Document.new

      # Company header
      pdf.text "COMPANY NAME", size: 18, align: :center, style: :bold
      pdf.text "Salary Slip", size: 14, align: :center
      pdf.text "Period: #{salary_calculation.period}", size: 12, align: :center
      pdf.move_down 20

      # Employee details
      pdf.text "Employee Details", size: 12, style: :bold
      pdf.text "Name: #{employee.name}"
      pdf.text "ID: #{employee.id}"
      pdf.text "Department: #{employee.department_name}"
      pdf.move_down 15

      # Salary details
      pdf.text "Salary Details", size: 12, style: :bold

      # Create a table for earnings
      earnings_data = [
        [ "Earnings", "Amount (JOD)" ],
        [ "Basic Salary", format("%.2f", salary_calculation.salary_package.base_salary) ],
        [ "Housing Allowance", format("%.2f", salary_calculation.salary_package.housing_allowance) ],
        [ "Transportation Allowance", format("%.2f", salary_calculation.salary_package.transportation_allowance) ],
        [ "Other Allowances", format("%.2f", salary_calculation.salary_package.other_allowances) ],
        [ "Gross Salary", format("%.2f", salary_calculation.gross_salary) ]
      ]

      pdf.table(earnings_data, width: 300) do
        cells.padding = 5
        cells.borders = []
        row(0).font_style = :bold
        row(0).borders = [ :bottom ]
        row(-1).borders = [ :top ]
        row(-1).font_style = :bold
      end

      pdf.move_down 15

      # Create a table for deductions
      deductions = salary_calculation.deductions
      deductions_data = [
        [ "Deductions", "Amount (JOD)" ],
        [ "Income Tax", format("%.2f", deductions["income_tax"].to_f) ],
        [ "Social Security", format("%.2f", deductions["social_security"].to_f) ],
        [ "Attendance Deductions", format("%.2f", deductions["attendance"].to_f) ],
        [ "Leave Deductions", format("%.2f", deductions["leave"].to_f) ],
        [ "Other Deductions", format("%.2f", deductions["other"].to_f) ],
        [ "Total Deductions", format("%.2f", salary_calculation.total_deductions) ]
      ]

      pdf.table(deductions_data, width: 300) do
        cells.padding = 5
        cells.borders = []
        row(0).font_style = :bold
        row(0).borders = [ :bottom ]
        row(-1).borders = [ :top ]
        row(-1).font_style = :bold
      end

      pdf.move_down 15

      # Net salary
      pdf.text "Net Salary: JOD #{format("%.2f", salary_calculation.net_salary)}", size: 14, style: :bold

      # Payment details
      pdf.move_down 15
      pdf.text "Payment Details", size: 12, style: :bold
      pdf.text "Payment Date: #{salary_calculation.payment_date&.strftime('%d/%m/%Y') || 'Pending'}"
      pdf.text "Payment Method: Bank Transfer"

      # Footer
      pdf.move_down 30
      pdf.text "This is a computer-generated document and does not require a signature.", size: 8, align: :center

      # Return the PDF data
      pdf.render
    end

    def attach_pdf(content)
      # Create a temp file with the PDF content
      temp_file = Tempfile.new([ 'salary_slip', '.pdf' ])
      temp_file.binmode
      temp_file.write(content)
      temp_file.rewind

      # Attach the temp file to the calculation
      salary_calculation.salary_slip_pdf.attach(
        io: temp_file,
        filename: "salary_slip_#{salary_calculation.employee_id}_#{salary_calculation.period}.pdf",
        content_type: 'application/pdf'
      )

      temp_file.close
      temp_file.unlink

      true
    end
  end
end
