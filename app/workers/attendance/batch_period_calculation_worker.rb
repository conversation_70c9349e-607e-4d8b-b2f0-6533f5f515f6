class Attendance::BatchPeriodCalculationWorker
  include Sidekiq::Worker

  sidekiq_options queue: :attendance, retry: 3

  def perform(start_date_string = nil, end_date_string = nil, employee_id = nil)
    # Default to yesterday if no dates provided
    start_date_string ||= Date.yesterday.to_s
    end_date_string ||= Date.yesterday.to_s
    begin
      # Log the input parameters for debugging
      log_start_info(start_date_string, end_date_string, employee_id)

      # Parse the dates and find employees
      start_date, end_date = parse_dates(start_date_string, end_date_string)
      employees = find_employees(employee_id)

      # Queue individual jobs for each employee-day
      job_count = queue_calculation_jobs(employees, start_date, end_date)

      # Log completion
      log_completion(job_count, employees.count, start_date, end_date)
    rescue => e
      log_error(e)
      raise e
    end
  end

  private

  def log_start_info(start_date_string, end_date_string, employee_id)
    Rails.logger.info("Starting batch attendance period calculation for start_date: #{start_date_string.inspect}, end_date: #{end_date_string.inspect}, employee_id: #{employee_id.inspect}")
  end

  def parse_dates(start_date_string, end_date_string)
    start_date = start_date_string.is_a?(String) ? Date.parse(start_date_string) : start_date_string.to_date
    end_date = end_date_string.is_a?(String) ? Date.parse(end_date_string) : end_date_string.to_date
    [ start_date, end_date ]
  end

  def find_employees(employee_id)
    if employee_id
      [ Employee.find(employee_id) ]
    else
      Employee.all
    end
  rescue => e
    Rails.logger.error("Failed to find employees: #{e.message}")
    # For development purposes, create a list of employee IDs
    employee_ids = employee_id ? [ employee_id.to_i ] : (1..10).to_a
    employee_ids.map { |id| OpenStruct.new(id: id) }
  end

  def queue_calculation_jobs(employees, start_date, end_date)
    job_count = 0
    employees.each do |employee|
      (start_date..end_date).each do |date|
        # Convert date to string to ensure it's a native JSON type
        date_string = date.to_s
        PeriodCalculationWorker.perform_async(employee.id, date_string)
        job_count += 1
      end
    end
    job_count
  end

  def log_completion(job_count, employee_count, start_date, end_date)
    Rails.logger.info("Queued #{job_count} attendance period calculations for #{employee_count} employees from #{start_date} to #{end_date}")
  end

  def log_error(error)
    Rails.logger.error("Error queueing attendance period calculations: #{error.class.name}: #{error.message}")
    Rails.logger.error(error.backtrace.join("\n"))
  end
end
