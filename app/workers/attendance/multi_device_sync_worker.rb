class AttendanceMultiDeviceSyncWorker
  include Sidekiq::Worker
  sidekiq_options queue: :attendance_orchestrator, retry: 2

  def perform(options = {})
    start_date = options['start_date']&.to_date || Date.yesterday
    end_date = options['end_date']&.to_date || Date.today
    device_ids = options['device_ids'] || []
    sync_type = options['sync_type'] || 'scheduled'
    triggered_by_id = options['triggered_by_id']

    Rails.logger.info("Starting multi-device sync for #{device_ids.any? ? device_ids.count : 'all'} devices")

    # Get devices to sync
    devices = get_devices_to_sync(device_ids)

    if devices.empty?
      Rails.logger.warn("No active devices found for sync")
      return { message: "No active devices found", devices_synced: 0 }
    end

    # Create sync logs for all devices
    sync_logs = create_sync_logs(devices, start_date, end_date, sync_type, triggered_by_id)

    # Queue individual device sync jobs
    job_ids = queue_device_sync_jobs(sync_logs)

    # Monitor sync progress (optional)
    monitor_sync_progress(sync_logs) if options['monitor_progress']

    Rails.logger.info("Queued #{job_ids.count} device sync jobs")

    {
      message: "Multi-device sync initiated successfully",
      devices_synced: devices.count,
      sync_log_ids: sync_logs.map(&:id),
      job_ids: job_ids
    }

  rescue => e
    Rails.logger.error("Multi-device sync failed: #{e.message}")
    Rails.logger.error(e.backtrace.join("\n"))
    raise e
  end

  private

  def get_devices_to_sync(device_ids)
    if device_ids.any?
      AttendanceDevice.where(id: device_ids, status: :active)
    else
      AttendanceDevice.active.where(
        sync_config: AttendanceDevice.arel_table[:sync_config].matches('%"auto_sync_enabled":true%')
      )
    end
  end

  def create_sync_logs(devices, start_date, end_date, sync_type, triggered_by_id)
    triggered_by = triggered_by_id ? Employee.find(triggered_by_id) : nil

    devices.map do |device|
      AttendanceSyncLog.create_for_sync(
        device,
        sync_type,
        { start_date: start_date, end_date: end_date },
        triggered_by
      )
    end
  end

  def queue_device_sync_jobs(sync_logs)
    sync_logs.map do |sync_log|
      # Add slight delay between jobs to prevent overwhelming devices
      delay = rand(0..30).seconds

      ::DeviceSyncWorker.perform_in(
        delay,
        sync_log.attendance_device_id,
        sync_log.id
      )
    end
  end

  def monitor_sync_progress(sync_logs)
    # This could be enhanced to provide real-time progress updates
    # For now, we'll just log the progress periodically

    AttendanceSyncProgressMonitorWorker.perform_in(
      1.minute,
      sync_logs.map(&:id)
    )
  end
end

# Optional: Progress monitoring worker
class AttendanceSyncProgressMonitorWorker
  include Sidekiq::Worker
  sidekiq_options queue: 'attendance_monitor', retry: 1

  def perform(sync_log_ids, check_count = 0)
    sync_logs = AttendanceSyncLog.where(id: sync_log_ids)

    completed_logs = sync_logs.where(status: [ :success, :failed, :partial ])
    pending_logs = sync_logs.where(status: [ :pending, :running ])

    Rails.logger.info("Sync Progress: #{completed_logs.count}/#{sync_logs.count} completed")

    # If there are still pending jobs and we haven't checked too many times
    if pending_logs.any? && check_count < 20
      # Check again in 30 seconds
      AttendanceSyncProgressMonitorWorker.perform_in(
        30.seconds,
        sync_log_ids,
        check_count + 1
      )
    else
      # Final report
      generate_sync_summary_report(sync_logs)
    end
  end

  private

  def generate_sync_summary_report(sync_logs)
    summary = {
      total_devices: sync_logs.count,
      successful: sync_logs.successful.count,
      failed: sync_logs.failed.count,
      partial: sync_logs.where(status: :partial).count,
      total_records_processed: sync_logs.sum { |log| log.result_summary['total_processed'] || 0 },
      total_records_imported: sync_logs.sum { |log| log.result_summary['success'] || 0 },
      average_sync_time: calculate_average_sync_time(sync_logs.where.not(completed_at: nil))
    }

    Rails.logger.info("Multi-Device Sync Summary: #{summary}")

    # Could send notifications, update dashboard, etc.
    broadcast_sync_summary(summary)
  end

  def calculate_average_sync_time(completed_logs)
    return 0 if completed_logs.empty?

    total_duration = completed_logs.sum(&:duration)
    (total_duration / completed_logs.count).round(2)
  end

  def broadcast_sync_summary(summary)
    # This could broadcast to WebSocket channels, send emails, etc.
    # For now, we'll just log it
    Rails.logger.info("Broadcasting sync summary: #{summary}")
  end
end
