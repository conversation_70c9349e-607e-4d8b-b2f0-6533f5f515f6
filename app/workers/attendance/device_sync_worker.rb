class Attendance::DeviceSyncWorker
  include Sidekiq::Worker

  sidekiq_options queue: :attendance, retry: 3

  def perform(device_id, sync_log_id = nil)
    device = Attendance::Device.find(device_id)
    sync_log = sync_log_id ? Attendance::SyncLog.find(sync_log_id) : nil

    Rails.logger.info("Starting sync for device: #{device.name} (ID: #{device_id})")

    # Mark sync as running
    sync_log&.mark_as_running!

    # Determine sync type and perform appropriate sync
    if sync_log&.is_employee_sync?
      results = perform_employee_sync(device, sync_log)
    else
      results = sync_device_data(device, sync_log)
    end

    # Mark sync as completed
    sync_log&.mark_as_completed!(results)

    # Update device status
    device.update(status: :active, last_seen_at: Time.current)

    Rails.logger.info("Sync completed for device: #{device.name}. Results: #{results}")
    results

  rescue => e
    Rails.logger.error("Sync failed for device #{device&.name}: #{e.message}")
    Rails.logger.error(e.backtrace.join("\n"))

    # Handle employee sync failures with retry logic
    if sync_log&.is_employee_sync?
      sync_log.mark_failed_with_retry!(e.message)
    else
      sync_log&.mark_as_failed!(e)
    end

    # Update device status to error
    device&.update(status: :error)

    raise e
  end

  private

  # Employee sync method (System → Device)
  def perform_employee_sync(device, sync_log)
    operation = sync_log.operation_type
    employee = sync_log.employee
    sync_params = sync_log.sync_params

    # Handle case where employee might not exist (for testing)
    employee_name = employee ? (employee.user&.name || "Employee #{employee.id}") : "Employee #{sync_params['employee_id']}"
    Rails.logger.info("Performing #{operation} for employee #{employee_name} on device #{device.name}")

    # Create adapter
    adapter = device.create_adapter

    # Check if device supports user management
    unless adapter.supports_user_management?
      raise "Device #{device.name} does not support user management"
    end

    # Perform the operation
    case operation
    when 'create_user'
      result = adapter.create_user(sync_params['user_data'])

      # Create device mapping if successful
      if result
        device.employee_device_mappings.find_or_create_by(employee: employee) do |mapping|
          mapping.device_user_id = sync_params['device_user_id']
          mapping.notes = "Auto-created by employee sync"
        end
      end

    when 'update_user'
      result = adapter.update_user(sync_params['device_user_id'], sync_params['user_data'])

    when 'delete_user'
      result = adapter.delete_user(sync_params['device_user_id'])

      # Remove device mapping if successful
      if result
        device.employee_device_mappings.where(employee: employee).destroy_all
      end

    else
      raise "Unknown employee sync operation: #{operation}"
    end

    # Return results
    {
      operation: operation,
      employee_id: employee ? employee.id : sync_params['employee_id'],
      employee_name: employee_name,
      device_user_id: sync_params['device_user_id'],
      success: result ? 1 : 0,
      failure: result ? 0 : 1,
      result: result
    }
  end

  def sync_device_data(device, sync_log)
    sync_params = sync_log&.sync_params || {}
    start_date = sync_params['start_date']&.to_date || Date.yesterday
    end_date = sync_params['end_date']&.to_date || Date.today

    Rails.logger.info("Syncing data for #{device.name} from #{start_date} to #{end_date}")

    # Create adapter and fetch data
    adapter = device.create_adapter
    raw_records = adapter.fetch_data(start_date, end_date)

    Rails.logger.info("Retrieved #{raw_records.count} raw records from #{device.name}")

    # Process and import records
    results = import_attendance_records(raw_records, device, sync_log)

    # Trigger resolution for imported records (if ResolutionWorker exists)
    if results[:success] > 0
      Rails.logger.info("Triggering attendance resolution for imported records")
      if defined?(ResolutionWorker)
        ResolutionWorker.perform_async(start_date.to_s, end_date.to_s)
      else
        Rails.logger.info("ResolutionWorker not found, skipping resolution trigger")
      end
    end

    results
  end

  def import_attendance_records(raw_records, device, sync_log)
    results = {
      success: 0,
      failure: 0,
      duplicate: 0,
      total_processed: raw_records.count,
      errors: []
    }

    raw_records.each_with_index do |record_data, index|
      begin
        # Find employee by device code
        employee = find_employee_by_code(record_data[:employee_code], device)

        unless employee
          error_msg = "Employee not found for code: #{record_data[:employee_code]}"
          results[:errors] << error_msg
          sync_log&.add_error(error_msg, { record_index: index, record_data: record_data })
          results[:failure] += 1
          next
        end

        # Check for duplicate
        existing_event = find_existing_event(employee, record_data)
        if existing_event
          Rails.logger.debug("Duplicate event found for employee #{employee.id} at #{record_data[:timestamp]}")
          results[:duplicate] += 1
          next
        end

        # Create attendance event
        attendance_event = create_attendance_event(employee, record_data, device)

        if attendance_event.persisted?
          results[:success] += 1
          Rails.logger.debug("Created attendance event: #{attendance_event.id}")
        else
          error_msg = "Failed to create attendance event: #{attendance_event.errors.full_messages.join(', ')}"
          results[:errors] << error_msg
          sync_log&.add_error(error_msg, {
            record_index: index,
            record_data: record_data,
            validation_errors: attendance_event.errors.full_messages
          })
          results[:failure] += 1
        end

      rescue => e
        error_msg = "Error processing record #{index}: #{e.message}"
        results[:errors] << error_msg
        sync_log&.add_error(error_msg, {
          record_index: index,
          record_data: record_data,
          exception: e.class.name
        })
        results[:failure] += 1
        Rails.logger.error("Error processing attendance record: #{e.message}")
      end
    end

    Rails.logger.info("Import results for #{device.name}: #{results}")
    results
  end

  def find_employee_by_code(employee_code, device)
    return nil if employee_code.blank?

    # Primary: Device-specific mapping (new multi-device support)
    mapping = EmployeeDeviceMapping.find_by(
      attendance_device: device,
      device_user_id: employee_code
    )
    return mapping.employee if mapping

    # Fallback: Legacy device_code field (removed - no longer used)
    # Employee.find_by(device_code: employee_code) ||

    # Last resort: Direct ID match
    Employee.find_by(id: employee_code)
  end

  def find_existing_event(employee, record_data)
    # Check for duplicate within a small time window (±5 minutes)
    time_window = 5.minutes
    timestamp = record_data[:timestamp]

    Attendance::Event.where(
      employee: employee,
      timestamp: (timestamp - time_window)..(timestamp + time_window)
    ).first
  end

  def create_attendance_event(employee, record_data, device)
    Attendance::Event.create(
      employee: employee,
      timestamp: record_data[:timestamp],
      event_type: map_status_to_event_type(record_data[:status]),
      location: record_data[:location],
      source_device_id: device.id,
      raw_data: record_data[:raw_data]&.to_json
    )
  end

  def map_status_to_event_type(status)
    case status.to_s.downcase
    when 'check_in'
      :check_in
    when 'check_out'
      :check_out
    else
      :undetermined
    end
  end
end
