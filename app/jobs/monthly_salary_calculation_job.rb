class MonthlySalaryCalculationJob < ApplicationJob
  queue_as :default

  def perform
    # Calculate for current month by default
    period = Date.today.strftime("%Y-%m")

    results = {
      success: [],
      failure: []
    }

    Employee.active.each do |employee|
      service = Salary::CalculationService.new(employee, period)
      calculation = service.calculate

      if calculation
        results[:success] << { employee_id: employee.id, calculation_id: calculation.id }
      else
        results[:failure] << { employee_id: employee.id, errors: service.errors }
      end
    end

    # Log results
    Rails.logger.info("Monthly salary calculation completed: #{results[:success].count} succeeded, #{results[:failure].count} failed")

    # Return results
    results
  end
end
