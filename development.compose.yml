name: athar-people

services:
  app:
    hostname: people-app
    build:
      context: ./
      dockerfile: ./development.dockerfile
      args:
        BUNDLE_GEM__FURY__IO: ${BUNDLE_GEM__FURY__IO}
    #command: tail -f /dev/null
#    command: debug
    command: server
    env_file:
      - docker-compose.env
    ports:
      - "1235:1234"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.people.rule=Host(`people.athar.test`)"
      - "traefik.http.routers.people.entrypoints=web,websecure"
      - "traefik.http.services.people.loadbalancer.server.port=3002"
      - "traefik.http.routers.people.tls=false"

      # Health Check Configuration
#      - "traefik.http.services.people.loadbalancer.healthcheck.path=/up"
#      - "traefik.http.services.people.loadbalancer.healthcheck.interval=10s"
#      - "traefik.http.services.people.loadbalancer.healthcheck.timeout=3s"
    networks:
      - athar-network
    depends_on:
      - people-db
    volumes:
      - ./config/master.key:/rails/config/master.key
      - ./:/rails
      - /Users/<USER>/workspace/athar/athar-ems/gems/auth-gem:/Users/<USER>/workspace/athar/athar-ems/gems/auth-gem
      - /Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem:/Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem
      - /Users/<USER>/workspace/athar/athar-ems/gems/commons-gem:/Users/<USER>/workspace/athar/athar-ems/gems/commons-gem
      - /Users/<USER>/workspace/rbzk:/Users/<USER>/workspace/rbzk

  sidekiq:
    hostname: people-sidekiq
    build:
      context: ./
      dockerfile: ./development.dockerfile
      args:
        BUNDLE_GEM__FURY__IO: ${BUNDLE_GEM__FURY__IO}
    command: bundle exec sidekiq -C config/sidekiq.yml
    env_file:
      - docker-compose.env
    networks:
      - athar-network
    depends_on:
      - people-db
      - redis
    volumes:
      - ./config/master.key:/rails/config/master.key
      - ./:/rails
      - /Users/<USER>/workspace/athar/athar-ems/gems/auth-gem:/Users/<USER>/workspace/athar/athar-ems/gems/auth-gem
      - /Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem:/Users/<USER>/workspace/athar/athar-ems/gems/rpc-gem
      - /Users/<USER>/workspace/athar/athar-ems/gems/commons-gem:/Users/<USER>/workspace/athar/athar-ems/gems/commons-gem
    labels:
      - "traefik.enable=false"

  people-db:
    image: postgres:17
    container_name: people-db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: people_athar_db
    networks:
      - athar-network
    ports:
      - "5434:5432"
    labels:
      - "traefik.enable=false"

  redis:
    image: redis:7-alpine
    container_name: people-redis
    networks:
      - athar-network
    ports:
      - "6379:6379"
    labels:
      - "traefik.enable=false"

networks:
  athar-network:
    external: true
    name: athar-backend_athar-network
